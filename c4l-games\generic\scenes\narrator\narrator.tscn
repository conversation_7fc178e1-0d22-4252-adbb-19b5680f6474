[gd_scene load_steps=6 format=3 uid="uid://bw1hwysqa14ib"]

[ext_resource type="Script" uid="uid://brjph3abrrci" path="res://generic/scenes/narrator/narrator.gd" id="1_gyjfr"]
[ext_resource type="Script" uid="uid://drelty7gkyllo" path="res://generic/scenes/narrator/sub_title_panel.gd" id="2_gyjfr"]

[sub_resource type="Animation" id="Animation_gyjfr"]
length = 0.001

[sub_resource type="Animation" id="Animation_hlt6f"]
resource_name = "narration"
length = 10.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AudioStreamPlayer:playing")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0.0333333),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SubTitlePanel/MarginContainer/SubTitle:text")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 1.86667, 5.06667, 8.86667),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": ["It is time to get on the bus", "Click the school to count the students and get them on the bus", "When you are done, tell the crossing guard that the busses are ready to go", "Zero"]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_4bf7p"]
_data = {
&"RESET": SubResource("Animation_gyjfr"),
&"narration": SubResource("Animation_hlt6f")
}

[node name="Narrator" type="Node2D"]
script = ExtResource("1_gyjfr")

[node name="SubTitlePanel" type="PanelContainer" parent="."]
visible = false
offset_right = 40.0
offset_bottom = 40.0
script = ExtResource("2_gyjfr")

[node name="MarginContainer" type="MarginContainer" parent="SubTitlePanel"]
layout_mode = 2
theme_override_constants/margin_left = 5
theme_override_constants/margin_right = 5

[node name="SubTitle" type="Label" parent="SubTitlePanel/MarginContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="."]

[node name="NarratorAnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_4bf7p")
}
