[gd_scene load_steps=13 format=3 uid="uid://b30idc4j0gwl7"]

[ext_resource type="PackedScene" uid="uid://ddr16u6xeh7uq" path="res://levels/game_center/assets/scenes/game_selector/unit_1_week_3_selector.tscn" id="1_fwrgx"]
[ext_resource type="Script" uid="uid://c8i6mb6wu4g33" path="res://levels/game_center/game_center.gd" id="1_rhkba"]
[ext_resource type="PackedScene" uid="uid://bcnxtrk04t7gv" path="res://levels/game_center/assets/scenes/game_selector/unit_1_week_4_selector.tscn" id="3_khmne"]
[ext_resource type="PackedScene" uid="uid://c57xwoxyf5lu2" path="res://levels/game_center/assets/scenes/game_selector/unit_1_week_5_selector.tscn" id="5_ik64p"]
[ext_resource type="PackedScene" uid="uid://kg7kbfksktsq" path="res://levels/game_center/assets/scenes/game_selector/unit_2_week_2_picnic_selector.tscn" id="5_lwgl5"]
[ext_resource type="PackedScene" uid="uid://bfkjf5dlctcaj" path="res://levels/game_center/assets/scenes/game_selector/unit_4_week_1_picnic_selector.tscn" id="6_lwgl5"]
[ext_resource type="PackedScene" uid="uid://1sbqbcquf3pm" path="res://levels/game_center/assets/scenes/game_selector/unit_4_week_6_selector.tscn" id="7_wdjdg"]
[ext_resource type="PackedScene" uid="uid://bwergko4pqjl2" path="res://levels/game_center/assets/scenes/game_selector/unit_2_week_2_stand_selector.tscn" id="8_iky6s"]
[ext_resource type="PackedScene" uid="uid://c24ng4kv2hyhv" path="res://levels/game_center/assets/scenes/game_selector/unit_2_week_6_stand_selector.tscn" id="9_pqcf1"]
[ext_resource type="PackedScene" uid="uid://b18x7im1bu2bh" path="res://levels/game_center/assets/scenes/game_selector/unit_4_week_2_stand_selector.tscn" id="10_pqcf1"]
[ext_resource type="PackedScene" uid="uid://b2kjpq7y82172" path="res://levels/game_center/assets/scenes/game_selector/unit_5_weel_4_stand_v_1_selector.tscn" id="11_u6w1y"]
[ext_resource type="PackedScene" uid="uid://bav6g1r6gktco" path="res://levels/game_center/assets/scenes/game_selector/unit_5_week_4_stand_v_2_selector.tscn" id="12_y1fhj"]

[node name="GameCenter" type="Node2D"]
script = ExtResource("1_rhkba")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Control" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="ChildNameLabel" type="Label" parent="CanvasLayer/Control"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -20.0
offset_right = 20.0
offset_bottom = 45.0
grow_horizontal = 2
text = "TEST NAME"

[node name="ScrollContainer" type="ScrollContainer" parent="CanvasLayer/Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 209.0
grow_horizontal = 2
grow_vertical = 2

[node name="GridContainer" type="GridContainer" parent="CanvasLayer/Control/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 0
columns = 3

[node name="MarginContainer" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit1Week3Selector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer" instance=ExtResource("1_fwrgx")]
layout_mode = 2
game_scene = ""

[node name="MarginContainer2" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit1Week4Selector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer2" instance=ExtResource("3_khmne")]
layout_mode = 2

[node name="MarginContainer3" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit1Week5Selector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer3" instance=ExtResource("5_ik64p")]
layout_mode = 2

[node name="MarginContainer7" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit2Week2StandSelector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer7" instance=ExtResource("8_iky6s")]
layout_mode = 2

[node name="MarginContainer4" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit2Week2PicnicSelector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer4" instance=ExtResource("5_lwgl5")]
layout_mode = 2

[node name="MarginContainer8" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit2Week6StandSelector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer8" instance=ExtResource("9_pqcf1")]
layout_mode = 2

[node name="MarginContainer5" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit4Week1PicnicSelector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer5" instance=ExtResource("6_lwgl5")]
layout_mode = 2

[node name="MarginContainer9" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit4Week2StandSelector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer9" instance=ExtResource("10_pqcf1")]
layout_mode = 2

[node name="MarginContainer6" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit4Week6Selector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer6" instance=ExtResource("7_wdjdg")]
layout_mode = 2

[node name="MarginContainer10" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit5Weel4StandV1Selector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer10" instance=ExtResource("11_u6w1y")]
layout_mode = 2

[node name="MarginContainer11" type="MarginContainer" parent="CanvasLayer/Control/ScrollContainer/GridContainer"]
layout_mode = 2

[node name="Unit5Week4StandV2Selector" parent="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer11" instance=ExtResource("12_y1fhj")]
layout_mode = 2

[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer/Unit1Week3Selector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer2/Unit1Week4Selector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer3/Unit1Week5Selector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer7/Unit2Week2StandSelector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer4/Unit2Week2PicnicSelector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer8/Unit2Week6StandSelector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer5/Unit4Week1PicnicSelector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer9/Unit4Week2StandSelector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer6/Unit4Week6Selector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer10/Unit5Weel4StandV1Selector" to="." method="_on_selector_clicked"]
[connection signal="selector_clicked" from="CanvasLayer/Control/ScrollContainer/GridContainer/MarginContainer11/Unit5Week4StandV2Selector" to="." method="_on_selector_clicked"]
