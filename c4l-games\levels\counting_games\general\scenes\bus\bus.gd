extends CharacterBody2D

signal bus_returned

var pre_load_blue_child: PackedScene = preload("res://levels/counting_games/general/scenes/students/sitting_students/blue_sitting_student.tscn")
var pre_load_red_child: PackedScene = preload("res://levels/counting_games/general/scenes/students/sitting_students/red_sitting_student.tscn")
var pre_load_purple_child: PackedScene = preload("res://levels/counting_games/general/scenes/students/sitting_students/purple_sitting_student.tscn")
var pre_load_yellow_child: PackedScene = preload("res://levels/counting_games/general/scenes/students/sitting_students/yellow_sitting_student.tscn")

const child_blue_value: int = 1
const child_red_value: int = 2
const child_purple_value: int = 3
const child_yellow_value: int = 4

var child_seat_array: Array[Marker2D]
var next_avaialbe_seat: int
var child_passengers: Array[Node2D]

var is_leaving: bool
var speed: int
var starting_y_value: int = 10000

var has_returned: bool

# Called when the node enters the scene tree for the first time.
func _ready():
	child_seat_array = [$ChildSeat1, $ChildSeat2, $ChildSeat3, $ChildSeat4, $ChildSeat5, $ChildSeat6, $ChildSeat7, $ChildSeat8, $ChildSeat9, $ChildSeat10]
	has_returned = true
	reset()

func reset():
	next_avaialbe_seat = 0
	speed = 1000 + randi() % 50
	#remove children
	for local_child in child_passengers:
		remove_child(local_child)
		local_child.queue_free()
	child_passengers.clear()
	
	is_leaving = false
	
	
func start_leaving():
	speed = 375 + randi() % 50
	has_returned = false
	is_leaving = true
	
# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(_delta):
	if(is_leaving):
		move_off_screen()
	else:
		return_to_screen()

func move_off_screen():
	if(position.y < 10000):
		#direction
		var direction = Vector2.DOWN
		
		#velocity
		velocity = direction * speed
		
		#move and slide
		move_and_slide()
	
func return_to_screen():
	if(position.y > starting_y_value):
		#direction
		var direction = Vector2.UP
		
		#velocity
		velocity = direction * speed
		
		#move and slide
		move_and_slide()
		
	if position.y <= starting_y_value && !has_returned:
		has_returned = true
		bus_returned.emit()

func check_full_bus() -> bool:
	return next_avaialbe_seat > 9

func add_child_to_bus(child_type: int, has_hat: bool = false):
	
	if(!check_full_bus()):
		var new_child: SittingChild
		
		#Figure out child type
		match child_type:
			child_blue_value:
				new_child = pre_load_blue_child.instantiate() as SittingChild
			child_red_value:
				new_child = pre_load_red_child.instantiate() as SittingChild
			child_purple_value:
				new_child = pre_load_purple_child.instantiate() as SittingChild
			child_yellow_value:
				new_child = pre_load_yellow_child.instantiate() as SittingChild
		
		#Set Child position
		new_child.position = child_seat_array[next_avaialbe_seat].position
		next_avaialbe_seat += 1
		
		#Add child to scene
		child_passengers.append(new_child)
		add_child(new_child)
		new_child.set_has_hat(has_hat)
	
