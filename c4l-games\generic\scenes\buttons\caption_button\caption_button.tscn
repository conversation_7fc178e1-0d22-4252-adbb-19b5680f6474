[gd_scene load_steps=4 format=3 uid="uid://bh58tl2wuwhc4"]

[ext_resource type="Script" uid="uid://2iwthykiotc2" path="res://generic/scenes/buttons/caption_button/caption_button.gd" id="1_f17lt"]
[ext_resource type="Texture2D" uid="uid://b65o6a6u3lsq6" path="res://levels/counting_games/sprites/C4L–caption-button.png" id="1_ikwlc"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_ikwlc"]
size = Vector2(52, 32)

[node name="CaptionButton" type="Node2D"]
script = ExtResource("1_f17lt")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("1_ikwlc")
hframes = 4

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(2, 0)
shape = SubResource("RectangleShape2D_ikwlc")

[connection signal="input_event" from="Area2D" to="." method="_on_area_2d_input_event"]
[connection signal="mouse_entered" from="Area2D" to="." method="_on_area_2d_mouse_entered"]
[connection signal="mouse_exited" from="Area2D" to="." method="_on_area_2d_mouse_exited"]
