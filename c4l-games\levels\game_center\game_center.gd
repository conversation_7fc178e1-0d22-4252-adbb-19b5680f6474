extends Node2D

@onready var child_name_label := $CanvasLayer/Control/ChildNameLabel

#Web Fields
var api_base_url: String = ""
var http_request: HTTPRequest

#Game Param Fields
var unit: int = 0
var week: int = 0
var student_name: String = ""
var student_id: int = 0
var c4l_classroom_id: int = 0
var organization_id: int = 0

var is_debug_mode: bool = false

func read_app_settings():
	var result: Variant= JavaScriptBridge.eval("window.GODOT_CONFIG_JSON", true)

	if result is String:
		var parsed: Variant = JSON.parse_string(result)
		if parsed is Dictionary:
			is_debug_mode = parsed.get("debugMode", false)
			api_base_url = parsed.get("compassApiUrl", "")
		else:
			push_error("Parsed config is not a dictionary")
	else:
		push_error("Config JSON not found or not a string")

func _ready():
	read_app_settings()

	print("DebugMode: " + str(is_debug_mode))
	print("api_base_url: " + str(api_base_url))

	if !is_debug_mode:
		http_request = HTTPRequest.new()
		add_child(http_request)
		http_request.request_completed.connect(_on_token_validation_completed)

		if OS.has_feature("web"):
			get_and_validate_token()
		else:
			print("Not running in web environment")
	else:
		print("DEBUG MODE")

func get_and_validate_token() -> void:
	print("Starting token validation...")

	# First, try a simple JavaScript test
	var test_result = JavaScriptBridge.eval("'JavaScript is working'")
	print("JavaScript test result: ", test_result)

	# JavaScript code to get the access token from URL
	var js_code: String = """
	(function() {
		try {
			console.log('JavaScript function started');

			const params = new URLSearchParams(window.location.search);
			const token = params.get('access_token');

			// Debug information
			console.log('Full URL:', window.location.href);
			console.log('Search string:', window.location.search);
			console.log('All params:', Array.from(params.entries()));
			console.log('Token found:', token);

			const result = {
				token: token || '',
				hasToken: token !== null && token !== '',
				fullUrl: window.location.href,
				searchString: window.location.search,
				success: true
			};

			console.log('Returning result:', result);
			// Return as JSON string to avoid serialization issues
			return JSON.stringify(result);
		} catch (error) {
			console.error('JavaScript error:', error);
			const errorResult = {
				success: false,
				error: error.toString(),
				token: '',
				hasToken: false
			};
			return JSON.stringify(errorResult);
		}
	})();
	"""

	print("Executing JavaScript...")
	# Execute JavaScript
	var result_variant: Variant = JavaScriptBridge.eval(js_code)

	print("JavaScript execution completed. Result type: ", typeof(result_variant))
	print("Raw result: ", result_variant)

	if result_variant == null:
		show_error("Failed to read URL parameters - JavaScript returned null")
		return

	# Parse JSON string returned from JavaScript
	var json_string: String = str(result_variant)
	print("JSON string from JavaScript: ", json_string)

	if json_string.is_empty():
		show_error("Empty result from JavaScript")
		return

	var json: JSON = JSON.new()
	var parse_result: Error = json.parse(json_string)

	if parse_result != OK:
		show_error("Failed to parse JavaScript result as JSON")
		print("JSON parse error: ", json.get_error_message())
		return

	# Cast to Dictionary
	var result_dict: Dictionary = json.data as Dictionary

	# Debug output
	print("JavaScript result dictionary: ", result_dict)
	print("Dictionary keys: ", result_dict.keys())
	print("Full URL from JS: ", result_dict.get("fullUrl", ""))
	print("Search string from JS: ", result_dict.get("searchString", ""))
	print("Success flag: ", result_dict.get("success", false))

	var has_token: bool = bool(result_dict.get("hasToken", false))
	print("Has token: ", has_token)
	print("Token value: ", result_dict.get("token", ""))

	if not has_token:
		show_error("No access token provided")
		return

	var token: String = str(result_dict.get("token", ""))

	if token.is_empty():
		show_error("Invalid access token")
		return

	# Validate token with your API
	validate_token(token)

func validate_token(token: String) -> void:
	print("Validating token...")
	print("api_base_url: ", api_base_url)
	print("Token to validate: ", token)

	# URL encode the token to handle any special characters
	var encoded_token: String = token.uri_encode()
	print("Encoded token: ", encoded_token)

	# Construct the validation URL
	var validation_url: String = api_base_url + "/gametoken/validate?token=" + encoded_token
	print("Full validation URL: ", validation_url)

	# Set headers for the request
	var headers: PackedStringArray = PackedStringArray([
		"Content-Type: application/json",
		"Accept: application/json"
	])

	print("Request headers: ", headers)
	print("Making HTTP GET request...")

	# Make the HTTP request
	var error: Error = http_request.request(validation_url, headers, HTTPClient.METHOD_GET)

	if error != OK:
		show_error("Failed to connect to validation server")
		print("HTTP request error: ", error)

func _on_token_validation_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray) -> void:
	print("Token validation response received. Code: ", response_code)

	# Check if the request was successful
	if response_code != 200:
		var error_message: String
		match response_code:
			401:
				error_message = "Invalid or expired token"
			404:
				error_message = "Token not found"
			500:
				error_message = "Server error during validation"
			_:
				error_message = "Failed to validate token (Code: " + str(response_code) + ")"

		show_error(error_message)
		return

	# Parse the JSON response
	var body_string: String = body.get_string_from_utf8()

	if body_string.is_empty():
		show_error("Empty response from server")
		return

	var json: JSON = JSON.new()
	var parse_result: Error = json.parse(body_string)

	if parse_result != OK:
		show_error("Failed to parse server response")
		print("JSON parse error: ", json.get_error_message())
		return

	# Get the parsed data
	var response_data: Variant = json.data

	if response_data == null:
		show_error("Invalid server response")
		return

	# Cast to Dictionary
	var response_dict: Dictionary = response_data as Dictionary

	# Check if there's an error in the response
	if response_dict.has("error"):
		var error_message: String = str(response_dict.get("error", "Unknown error"))
		show_error(error_message)
		return

	# Extract game parameters with type safety
	unit = int(response_dict.get("unit", 0))
	week = int(response_dict.get("week", 0))
	student_name = str(response_dict.get("StudentName", ""))
	student_id = int(response_dict.get("StudentId", 0))
	c4l_classroom_id = int(response_dict.get("C4LClassroomId", 0))
	organization_id = int(response_dict.get("OrganizationId", 0))

	# Validate that we have all required data
	if unit <= 0 or week <= 0 or student_name.is_empty() or student_id <= 0:
		show_error("Invalid game data received")
		return

	# Load game content
	load_content()

func show_error(message: String) -> void:
	print("Error: ", message)
	#TODO Finish

func load_content() -> void:
	print("Loading game content...")
	child_name_label.text = student_name

func _on_selector_clicked(game_scene: String, unit: int, week: int, variation: int) -> void:
	print(game_scene)
	Globals.unit = unit
	Globals.week = week
	Globals.variation = variation
	var scene = load(game_scene)
	get_tree().change_scene_to_packed(scene)
