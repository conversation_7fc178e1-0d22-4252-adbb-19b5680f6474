[gd_scene load_steps=3 format=3 uid="uid://bx5iw8ee0rmrm"]

[ext_resource type="Script" uid="uid://p1gt7f1edn48" path="res://levels/counting_games/general/scenes/count_cloud/count_cloud.gd" id="1_1bgqd"]
[ext_resource type="Texture2D" uid="uid://c4y8fq2nsyq30" path="res://levels/counting_games/sprites/numeral-cloud.png" id="1_wfo0d"]

[node name="CountCloud" type="Node2D"]
script = ExtResource("1_1bgqd")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_wfo0d")

[node name="Label" type="Label" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -28.5
offset_top = -68.5
offset_right = 28.5
offset_bottom = 68.5
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 0
size_flags_vertical = 0
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 200
text = "0"
