extends Node2D

signal student_clicked

var has_selected: bool = false

func _ready() -> void:
	has_selected = false
	$Sprite2D.frame = 0
	
func reset() -> void:
	$Sprite2D.frame = 0
	has_selected = false

func _on_area_2d_input_event(_viewport: Node, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton && !has_selected && visible:
		if event.pressed:
			has_selected = true
			$Sprite2D.frame = 1
			student_clicked.emit()
