extends Node2D

func _init():
	randomize()
	
func drag_intro(fruit_slice_single: String, fruit_slice_plural: String, start_count: int) -> Array[String]:
	var created_drag_intro: Array[String]
	var intro_num: int = randi_range(0, 2)
	created_drag_intro.append("action_add_" + str(intro_num) + ".json")
	
	if intro_num == 0:
		created_drag_intro.append("number_" + str(start_count) + ".json")
		if start_count == 1:
			created_drag_intro.append(fruit_slice_single)
		else:
			created_drag_intro.append(fruit_slice_plural)
	else:
		created_drag_intro.append(fruit_slice_plural)
		
	return created_drag_intro
	
func generate_round_intro(fruit_slice_single: String, fruit_slice_plural: String, start_count: int) -> Array[String]:
	var round_intro: Array[String]
	var action_num: int = randi_range(0, 3)
	
	match action_num:
		0:
			round_intro.append("action_drag_1.json")
			round_intro.append(fruit_slice_plural)
		1:
			round_intro.append("action_place_1.json")
			round_intro.append(fruit_slice_plural)
		2:
			var created_drag_intro: Array[String] = drag_intro(fruit_slice_single, fruit_slice_plural, start_count)
			for audio in created_drag_intro:
				round_intro.append(audio)
		3:
			var add_action: int = randi_range(0, 2)
			round_intro.append("action_add_" + str(add_action) + ".json")
			
			if add_action == 0:
				round_intro.append("number_" + str(start_count) + ".json")
				if start_count == 1:
					round_intro.append(fruit_slice_single)
				else:
					round_intro.append(fruit_slice_plural)
			else:
				round_intro.append(fruit_slice_plural)
	
	var plate_action: int = randi_range(0, 4)
	round_intro.append("target_plate_" + str(plate_action) + ".json")
	return round_intro

func generate_intro(fruit_slice_single: String, fruit_slice_plural: String, start_count: int) -> Array[String]:
	var created_intro: Array[String]
	var picnic_intro_num: int = randi_range(0, 2)
	created_intro.append("intro_picnic_" + str(picnic_intro_num) + ".json")
	
	var round_intro: Array[String] = generate_round_intro(fruit_slice_single, fruit_slice_plural, start_count)
	for json in round_intro:
		created_intro.append(json)
	return created_intro

func generate_produce(fruit_slice_single: String, fruit_slice_plural: String, start_count: int) -> Array[String]:
	var produce_jsons: Array[String]
	
	var produce_num: int = randi_range(0, 3)
	produce_jsons.append("statement_produce_" + str(produce_num) + ".json")
	
	if produce_num == 0 || produce_num == 2:
		produce_jsons.append("number_" + str(start_count) + ".json")
		if start_count == 1:
			produce_jsons.append(fruit_slice_single)
		else:
			produce_jsons.append(fruit_slice_plural)
			
		var plate_num: int = randi_range(0, 4)
		produce_jsons.append("target_plate_" + str(plate_num) + ".json")
	else:
		if start_count == 1:
			produce_jsons.append(fruit_slice_single)
		else:
			produce_jsons.append(fruit_slice_plural)
	
	return produce_jsons

func generate_round_end(fruit_slice_single: String, fruit_slice_plural: String, start_count: int) -> Array[String]:
	var round_end: Array[String]
	
	var start: int = randi_range(0, 2)
	var congrats_num: int = randi_range(0, 3)
	
	if start == 0:
		round_end.append("number_" + str(start_count) + ".json")
		round_end.append("congrats_" + str(congrats_num) + ".json")
	else:
		var produce_jsons: Array[String] = generate_produce(fruit_slice_single, fruit_slice_plural, start_count)
		for json in produce_jsons:
			round_end.append(json)
	
	return round_end

func generate_game_end(fruit_slice_plural: String) -> Array[String]:
	var end_jsons: Array[String]
	end_jsons.append("statement_produce_3.json")
	end_jsons.append(fruit_slice_plural)
	return end_jsons
	
func generate_help(fruit_slice_plural: String) -> Array[String]:
	var help_jsons: Array[String]
	help_jsons.append("action_drag_1.json")
	help_jsons.append(fruit_slice_plural)
	help_jsons.append("target_plate_0.json")
	
	var ok_num: int = randi_range(0, 1)
	help_jsons.append("action_click_" + str(ok_num) + ".json")
	return help_jsons

func generate_first_inccorect(fruit_slice_plural: String) -> Array[String]:
	var incorrect_jsons: Array[String]
	var response_num: int = randi_range(0, 2)
	
	match response_num:
		0:
			incorrect_jsons.append("action_try_0.json")
			
			var try_num: int = randi_range(0, 1)
			if try_num == 0:
				incorrect_jsons.append("action_count_slowly.json")
			else:
				incorrect_jsons.append("action_produce_surely.json")
				incorrect_jsons.append(fruit_slice_plural)
				incorrect_jsons.append("target_plate_2.json")
		1:
			incorrect_jsons.append("action_add_1.json")
			incorrect_jsons.append(fruit_slice_plural)
		2:
			incorrect_jsons.append("action_put.json")
			incorrect_jsons.append(fruit_slice_plural)
			incorrect_jsons.append("target_plate_3.json")
	
	return incorrect_jsons
	
func generate_second_inccorect(fruit_slice_plural: String, start_count: int) -> Array[String]:
	var incorrect_jsons: Array[String]
	
	incorrect_jsons.append("action_drag_0.json")
	incorrect_jsons.append(fruit_slice_plural)
	incorrect_jsons.append("target_plate_3.json")
	incorrect_jsons.append("until.json")
	
	if start_count == 1:
		incorrect_jsons.append("statement_is.json")
	else:
		incorrect_jsons.append("statement_are.json")
		
	incorrect_jsons.append("number_" + str(start_count) + ".json")
	
	return incorrect_jsons

func generate_third_inccorect(start_count: int) -> Array[String]:
	var incorrect_jsons: Array[String]
	
	incorrect_jsons.append("incorrect_0.json")
	incorrect_jsons.append("number_" + str(start_count) + ".json")
	incorrect_jsons.append("target_plate_4.json")
	
	return incorrect_jsons

func generate_plate_number(start_count: int) -> Array[String]:
	var json_array: Array[String]
	json_array.append("number_" + str(start_count) + ".json")
	return json_array
