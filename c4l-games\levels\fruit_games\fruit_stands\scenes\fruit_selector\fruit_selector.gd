extends Node2D

var pre_load_pear: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/pear.tscn")
var pre_load_lemon: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/lemon.tscn")
var pre_load_peach: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/peach.tscn")
var pre_load_plum: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/plum.tscn")
var pre_load_apple: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/apple.tscn")
var pre_load_orange: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/orange.tscn")
var pre_load_grapefruit: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/grapefruit.tscn")
var pre_load_lime: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/fruit/lime.tscn")

var pre_load_fruit: PackedScene
var fruit_singular: String
var fruit_plural: String

func set_pear() -> void:
	pre_load_fruit = pre_load_pear
	fruit_singular = "token_pear.json"
	fruit_plural = "token_pear_plural.json"

func set_lemon() -> void:
	pre_load_fruit = pre_load_lemon
	fruit_singular = "token_lemon.json"
	fruit_plural = "token_lemon_plural.json"

func set_peach() -> void:
	pre_load_fruit = pre_load_peach
	fruit_singular = "token_peach.json"
	fruit_plural = "token_peach_plural.json"

func set_plum() -> void:
	pre_load_fruit = pre_load_plum
	fruit_singular = "token_plum.json"
	fruit_plural = "token_plum_plural.json"

func set_apple() -> void:
	pre_load_fruit = pre_load_apple
	fruit_singular = "token_apple.json"
	fruit_plural = "token_apple_plural.json"

func set_orange() -> void:
	pre_load_fruit = pre_load_orange
	fruit_singular = "token_orange.json"
	fruit_plural = "token_orange_plural.json"

func set_grapefruit() -> void:
	pre_load_fruit = pre_load_grapefruit
	fruit_singular = "token_grapefruit.json"
	fruit_plural = "token_grapefruit_plural.json"

func set_lime() -> void:
	pre_load_fruit = pre_load_lime
	fruit_singular = "token_lime.json"
	fruit_plural = "token_lime_plural.json"
