extends Node2D

signal caption_clicked

@export var hasCC: bool = false;

func _on_area_2d_mouse_entered() -> void:
	$Sprite2D.frame = 1


func _on_area_2d_mouse_exited() -> void:
	if hasCC:
		$Sprite2D.frame = 2
	else:
		$Sprite2D.frame = 0


func _on_area_2d_input_event(_viewport: Node, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton:
		if event.pressed:
			$Sprite2D.frame = 2
			caption_clicked.emit()
		else:
			$Sprite2D.frame = 1
