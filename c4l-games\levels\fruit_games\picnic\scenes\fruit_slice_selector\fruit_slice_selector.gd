extends Node2D

var pre_load_apple_slice: PackedScene = preload("res://levels/fruit_games/picnic/scenes/slices/apple_slice.tscn")
var pre_load_grapefruit_slice: PackedScene = preload("res://levels/fruit_games/picnic/scenes/slices/grapefruit_slice.tscn")
var pre_load_orange_slice: PackedScene = preload("res://levels/fruit_games/picnic/scenes/slices/orange_slice.tscn")
var pre_load_kiwi_slice: PackedScene = preload("res://levels/fruit_games/picnic/scenes/slices/kiwi_slice.tscn")

var fruit_image: String
var fruit_slice_image: String
var pre_load_slice: PackedScene
var fruit_singular: String
var fruit_plural: String

func set_apple() -> void:
	pre_load_slice = pre_load_apple_slice
	fruit_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-fruit_apple.png"
	fruit_slice_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-slice_apple.png"
	fruit_singular = "token_appleSlice.json"
	fruit_plural = "token_appleSlice_plural.json"

func set_grapefruit() -> void:
	pre_load_slice = pre_load_grapefruit_slice
	fruit_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-fruit_grapefruit.png"
	fruit_slice_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-slice_grapefruit.png"
	fruit_singular = "token_grapefruitSlice.json"
	fruit_plural = "token_grapefruitSlice_plural.json"

func set_orange() -> void:
	pre_load_slice = pre_load_orange_slice
	fruit_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-fruit_orange.png"
	fruit_slice_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-slice_orange.png"
	fruit_singular = "token_orangeSlice.json"
	fruit_plural = "token_orangeSlice_plural.json"

func set_kiwi() -> void:
	pre_load_slice = pre_load_kiwi_slice
	fruit_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-fruit_kiwi.png"
	fruit_slice_image = "res://levels/fruit_games/sprites/C4L-ORC-Picnic-slice_kiwi.png"
	fruit_singular = "token_kiwiSlice.json"
	fruit_plural = "token_kiwiSlice_plural.json"
