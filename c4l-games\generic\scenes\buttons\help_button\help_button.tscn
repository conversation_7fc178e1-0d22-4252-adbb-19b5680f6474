[gd_scene load_steps=4 format=3 uid="uid://cakvwk6r6comh"]

[ext_resource type="Texture2D" uid="uid://0l0vvo0hopyi" path="res://levels/counting_games/sprites/help.png" id="1_smay5"]
[ext_resource type="Script" uid="uid://cgojtkddxm8fd" path="res://generic/scenes/buttons/help_button/help_button.gd" id="1_yu0s4"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_smay5"]
size = Vector2(132, 131)

[node name="HelpButton" type="Node2D"]
script = ExtResource("1_yu0s4")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("1_smay5")
hframes = 4

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(1, 0.5)
shape = SubResource("RectangleShape2D_smay5")

[connection signal="input_event" from="Area2D" to="." method="_on_area_2d_input_event"]
[connection signal="mouse_entered" from="Area2D" to="." method="_on_area_2d_mouse_entered"]
[connection signal="mouse_exited" from="Area2D" to="." method="_on_area_2d_mouse_exited"]
