extends Control
class_name GameSelector

@export var game_scene: String

@export var unit: int
@export var week: int
@export var variation: int

signal selector_clicked(game_scene, unit, week, variation)

func _on_gui_input(event: InputEvent) -> void:
	if event is InputEventMouseButton:
		if event.pressed && event.button_index == MOUSE_BUTTON_LEFT:
			selector_clicked.emit(game_scene, unit, week, variation)
			
