extends Node2D

var play_again: PackedScene = preload("res://levels/counting_games/get_ready_to_go/sub_levels/get_ready_to_go_play_again.tscn")

@onready var narrator = $Narrator
@onready var countCloud = $CountCloud
@onready var bus = $Bus
@onready var busAudio = $BusDriveAudio
@onready var timer = $Timer

var audioFilePath: String = "res://levels/counting_games/get_ready_to_go/audio/"
var jsonFilePath: String = "res://levels/counting_games/get_ready_to_go/closed_caption_jsons/"

var current_round: int
var student_array: Array[Node2D]
var max_students: int = 5
var next_child_type: int = 1

var caption_enabled: bool

func _ready():
	caption_enabled = false
	current_round = 1
	countCloud.count_limit = 10
	bus.starting_y_value = bus.position.y
	student_array = [$Students/StandingStudent1, $Students/StandingStudent2, $Students/StandingStudent3, $Students/StandingStudent4, $Students/StandingStudent5, 
					$Students/StandingStudent6, $Students/StandingStudent7, $Students/StandingStudent8, $Students/StandingStudent9, $Students/StandingStudent10]
	# We set the first round as 2 Children
	var jsonArray: Array[String] = ["intro.json"]
	narrator.play_narration(caption_enabled, jsonFilePath, jsonArray, audioFilePath)
	
	set_round(2)

func set_round(new_max: int) -> void:
	max_students = new_max
	for i in range(student_array.size()):
		if max_students > i:
			student_array[i].visible = true
		else:
			student_array[i].visible = false
		
		student_array[i].reset()

func pick_next_child():
	next_child_type = next_child_type + 1
	if (next_child_type > 4):
		next_child_type = 1

func _on_standing_student_student_clicked() -> void:
	countCloud.count_next()
	var count_audio: String = countCloud.get_count_audio()
	var jsonArray: Array[String] = [count_audio]
	narrator.play_narration(caption_enabled, jsonFilePath, jsonArray, audioFilePath)
	
	var currentCount: int = countCloud.get_count()
	if currentCount >= max_students:
		load_bus()
		
func reset():
	next_child_type = 1
	bus.reset()
	countCloud.reset_count()

func load_bus() -> void:
	for i in range(max_students):
		student_array[i].visible = false
		bus.add_child_to_bus(next_child_type, true)
		pick_next_child()
		await get_tree().create_timer(0.5).timeout
		
	start_reset_process()

func start_reset_process():
	bus.start_leaving()
	busAudio.play()
	narrator.stop_animation()
	timer.start()

func _on_timer_timeout() -> void:
	reset()
	current_round+=1
	if current_round > 3:
		get_tree().change_scene_to_packed(play_again)


func _on_bus_bus_returned() -> void:
	var new_max: int
	match current_round:
		1:
			new_max = 2
		2:
			new_max = 6
		3:
			new_max = 10
	
	set_round(new_max)


func _on_help_button_help_clicked() -> void:
	var jsonArray: Array[String] = ["instructions.json"]
	narrator.play_narration(caption_enabled, jsonFilePath, jsonArray, audioFilePath )


func _on_caption_button_caption_clicked() -> void:
	caption_enabled = !caption_enabled
	$CaptionButton.hasCC = caption_enabled
