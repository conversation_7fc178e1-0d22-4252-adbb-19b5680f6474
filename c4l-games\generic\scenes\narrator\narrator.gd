extends Node2D

@onready var label: Label = $SubTitlePanel/MarginContainer/SubTitle
@onready var animation_player: AnimationPlayer = $NarratorAnimationPlayer
@onready var audio_player: AudioStreamPlayer = $AudioStreamPlayer

var buffer_time: float = 1.0
var is_ready_for_playback: bool = true

## This must be done right before any timers are thrown
func stop_animation() -> void:
	# Stop any currently playing animation
	if animation_player.is_playing():
		animation_player.stop()

	# Clear existing animations from the default library
	var default_library: AnimationLibrary = animation_player.get_animation_library("") as AnimationLibrary
	if default_library:
		var animation_names: PackedStringArray = default_library.get_animation_list()
		for anim_name in animation_names:
			default_library.remove_animation(anim_name)

	label.text = ""

func play_narration(hasCC: bool, jsonFilePath: String, jsonFileArray: Array[String], audioFilePath: String) -> void:
	stop_animation();
	var language: String = "en"
	
	# Create a completely new AnimationPlayer each time
	if animation_player != null:
		animation_player.queue_free()
	
	var new_animation_player = AnimationPlayer.new()
	add_child(new_animation_player)
	animation_player = new_animation_player
	
	var new_anim_lib = AnimationLibrary.new()
	animation_player.add_animation_library("", new_anim_lib)
	
	# Create a new animation
	var animation: Animation = Animation.new()
	
	#TODO Loop here?
	var audio_start_point: float = 0.0
	for jsonFile: String in jsonFileArray:
		var jsonFullFilePath: String = jsonFilePath + language + "/" + jsonFile
		var audioNarration: AudioNarration = load_narration(jsonFullFilePath)

		#TODO Figure out language
		var audio_file: String = audioNarration.audio_file
		#TODO Solve audio path dynamically
		var audio_path = audioFilePath + language + "/" + audio_file
		var audioStream: AudioStreamWAV = load(audio_path)
		
		if audioStream != null:
			# Get the track index for the AudioStreamPlayer
			var audio_track_index: int = animation.add_track(Animation.TYPE_AUDIO)
			# Set the track's path to the AudioStreamPlayer node
			animation.track_set_path(audio_track_index, audio_player.get_path())
			# Add the audio key with the sound at time 0.0
			animation.audio_track_insert_key(audio_track_index, audio_start_point, audioStream)

			var captions: Array[ClosedCaption] = audioNarration.captions

			#If closed captions are on then show closed captions
			if hasCC && captions.size()>0:
				var sub_title_track_index: int = animation.add_track(Animation.TYPE_VALUE)
				# Set the track's path to the Label's text property
				animation.track_set_path(sub_title_track_index, "%s:text" % label.get_path())

				# IMPORTANT: Set the interpolation type to NEAREST for text
				animation.track_set_interpolation_type(sub_title_track_index, Animation.INTERPOLATION_NEAREST)

				# Add text keyframes at different times
				for caption in captions:
					var caption_key: float = audio_start_point+ caption.key
					animation.track_insert_key(sub_title_track_index, caption_key, caption.caption)

				# Make sure to set text to blank once audio is done
				animation.track_insert_key(sub_title_track_index, audioStream.get_length() + buffer_time, "")
				
			audio_start_point = audioStream.get_length() + audio_start_point

		#TODO set variable so we know the length of the previous audio file... then add them all together
		# Set animation length (adjust based on your audio length)
		animation.length = audio_start_point + buffer_time

	# Get the default animation library and add our animation
	var anim_lib: AnimationLibrary = animation_player.get_animation_library("") as AnimationLibrary
	anim_lib.add_animation("play_sound", animation)

	# Disconnect any existing connections to avoid duplicates
	if animation_player.has_signal("animation_finished") and animation_player.is_connected("animation_finished", _on_animation_finished):
		animation_player.animation_finished.disconnect(_on_animation_finished)

	# Connect to the animation_finished signal
	animation_player.animation_finished.connect(_on_animation_finished)

	# Mark as not ready for playback until animation finishes
	is_ready_for_playback = false

	# Play the animation
	animation_player.play("play_sound")

# For loading a single AudioNarration object
func load_narration(file_path: String) -> AudioNarration:
	var narration: AudioNarration = AudioNarration.new()

	# Check if file exists
	if not FileAccess.file_exists(file_path):
		printerr("Narration file not found: ", file_path)
		return narration

	# Open and read file
	var file: FileAccess = FileAccess.open(file_path, FileAccess.READ)
	var json_text: String = file.get_as_text()
	file.close()

	# Parse JSON
	var json: JSON= JSON.new()
	var error: Error= json.parse(json_text)

	if error != OK:
		printerr("JSON parse error: ", json.get_error_message(), " at line ", json.get_error_line())
		return narration

	# Get data from JSON
	var data = json.get_data()

	# Ensure data is a dictionary
	if not data is Dictionary:
		printerr("Invalid JSON format: expected dictionary")
		return narration

	# Extract audio_file
	if data.has("audio_file") and data.audio_file is String:
		narration.audio_file = data.audio_file

	# Extract captions
	if data.has("captions") and data.captions is Array:
		for item in data.captions:
			if not (item is Dictionary):
				continue

			if not (item.has("key") and item.has("caption")):
				continue

			var caption = ClosedCaption.new(float(item.key), item.caption)
			narration.captions.append(caption)

	return narration

## Called when an animation finishes playing
func _on_animation_finished(_anim_name: String) -> void:
	# Disconnect to avoid multiple connections
	if animation_player.is_connected("animation_finished", _on_animation_finished):
		animation_player.animation_finished.disconnect(_on_animation_finished)

	# Reinitialize the audio system
	_reinitialize_audio_system()

	# Mark as ready for next playback
	is_ready_for_playback = true

## Reinitializes the audio system to ensure it's ready for playback
func _reinitialize_audio_system() -> void:
	# Stop any current animations
	stop_animation()

	# Reset the audio player
	audio_player.stop()

	# Force the audio server to update
	AudioServer.get_time_to_next_mix()
	AudioServer.get_output_latency()

	# Create a dummy animation to "prime" the system
	var dummy_animation = Animation.new()
	var anim_lib = animation_player.get_animation_library("") as AnimationLibrary
	if anim_lib.has_animation("dummy"):
		anim_lib.remove_animation("dummy")
	anim_lib.add_animation("dummy", dummy_animation)

	# This is important - we need to ensure the animation system is ready
	is_ready_for_playback = true
