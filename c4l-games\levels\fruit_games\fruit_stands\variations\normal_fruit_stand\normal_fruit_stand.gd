extends Node2D

@onready var anim_player := $AnimationPlayer
@onready var fruit_selector := $FruitSelector
@onready var single_crate := $SingleCrate
@onready var round_manager := $NormalFruitStandRoundManager
@onready var large_crate := $LargeCrate
@onready var label := $CanvasLayer/Panel/Label
@onready var nar_manager := $FruitStandNarrationManager
@onready var narrator := $Narrator
@onready var help_button := $HelpButton
@onready var caption_button := $CaptionButton
@onready var fruit_count_label := $CanvasLayer2/MarginContainer/FruitCountLabel

var fruit_array: Array[Fruit]
var fruit_count: int
var caption_enabled: bool = false
var attempt: int
var has_starting_fruit: bool = false
var difficulty: int = 0

var audioFilePath: String = "res://levels/fruit_games/audio/"
var jsonFilePath: String = "res://levels/fruit_games/closed_caption_jsons/"

func select_fruit() -> void:
	if Globals.unit == 4 && Globals.week == 2:
		fruit_selector.set_plum()
		has_starting_fruit = false
		difficulty = 1
	elif Globals.unit == 5 && Globals.week == 4 && Globals.variation == 1:
		fruit_selector.set_grapefruit()
		has_starting_fruit = true
		difficulty = 0
	elif Globals.unit == 5 && Globals.week == 4 && Globals.variation == 2:
		fruit_selector.set_lime()
		has_starting_fruit = true
		difficulty = 1
	else:
		fruit_selector.set_lime()
		has_starting_fruit = true
		difficulty = 1

func _ready():
	attempt = 1
	help_button.sprite_2d.texture = load("res://levels/fruit_games/sprites/C4L-ORC-Help-Button.png")
	select_fruit()
	round_manager.new_round(has_starting_fruit, difficulty)
	fruit_count = round_manager.fruit_count
	label.text = str(fruit_count)
	fruit_count_label.text = str(fruit_count)
	var intro_jsons: Array[String] = nar_manager.generate_simple_intro(fruit_selector.fruit_singular, fruit_selector.fruit_plural, fruit_count)
	narrator.play_narration(caption_enabled, jsonFilePath, intro_jsons, audioFilePath)
	anim_player.play("intro")

func reset() -> void:
	for fruit in fruit_array:
		fruit.get_parent().remove_child(fruit)
		fruit.queue_free()
	fruit_array.clear()
	
	large_crate.reset()
	
func set_round() -> void:
	attempt = 1
	round_manager.new_round(has_starting_fruit, difficulty)
	fruit_count = round_manager.fruit_count
	label.text = str(fruit_count)
	fruit_count_label.text = str(fruit_count)
	anim_player.play("show_crates")
	
	if round_manager.round_number <= 3:
		var round_jsons: Array[String] = nar_manager.generate_simple_round_intro(fruit_selector.fruit_singular, fruit_selector.fruit_plural, fruit_count)
		narrator.play_narration(caption_enabled, jsonFilePath, round_jsons, audioFilePath)

func spawn_fruit() -> void:
	var fruit_spawn_count: int = fruit_count + 2
	
	if fruit_spawn_count > 10:
		fruit_spawn_count = 10
	
	var crate_fruit_count: int = fruit_spawn_count - round_manager.fruit_start
	
	for i in crate_fruit_count:
		var fruit: Fruit = fruit_selector.pre_load_fruit.instantiate() as Fruit
		
		fruit.position = single_crate.position
		#Shrinking relative size of slice
		fruit.scale = Vector2(0.8, 0.8)
		add_child(fruit)
		fruit.connect("check_drop_area", check_fruit_in_crate )
		fruit_array.append(fruit)
		fruit.spawn_animation()
		await get_tree().create_timer(0.2).timeout
		
	for i in round_manager.fruit_start:
		var fruit: Fruit = fruit_selector.pre_load_fruit.instantiate() as Fruit
		
		add_to_crate(fruit)
		
		#Shrinking relative size of slice
		fruit.scale = Vector2(0.8, 0.8)
		add_child(fruit)
		fruit.connect("check_drop_area", check_fruit_in_crate )
		fruit_array.append(fruit)
		fruit.spawn_animation()
		fruit.in_crate = true
		fruit.start_position = single_crate.position
		await get_tree().create_timer(0.2).timeout

func add_to_crate(fruit: Fruit) -> void:
	var currentIndex: int = large_crate.current_index
	#large_crate.add_child(fruit)
	var crate_point: Marker2D
	fruit.in_crate = true
	if fruit.crate_index == -1:
		crate_point = large_crate.set_crate_index_point()
		fruit.crate_index = currentIndex
	else:
		crate_point = large_crate.get_index_marker(fruit.crate_index)
	fruit.global_position = crate_point.global_position

func check_fruit_in_crate(fruit: Fruit) -> void:
	var crate_area := large_crate.get_node("Area2D")
	var fruit_area := fruit.get_node("Area2D")
	var in_crate: bool = crate_area.overlaps_area(fruit_area)
	
	if in_crate:
		add_to_crate(fruit)
	elif fruit.crate_index > -1:
		large_crate.remove_index()
		for otherFruit in fruit_array:
			if otherFruit.crate_index > fruit.crate_index:
				otherFruit.crate_index -= 1
				var new_marker: Marker2D = large_crate.get_index_marker(otherFruit.crate_index)
				otherFruit.in_crate = true
				otherFruit.global_position = new_marker.global_position
		
		fruit.crate_index = -1
		
func handle_invalid() -> void:
	if attempt == 1:
		var incorrect_jsons: Array[String] = nar_manager.generate_first_inccorect(fruit_selector.fruit_plural)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	elif attempt == 2:
		var incorrect_jsons: Array[String] = nar_manager.generate_second_inccorect(fruit_selector.fruit_plural, fruit_count)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	else:
		end_round(false)
		
	attempt += 1

func end_round(success: bool) -> void:
	for fruit in fruit_array:
		fruit.disabled = true
		if fruit.in_crate:
			var crate_position = fruit.global_position
			remove_child(fruit)
			large_crate.add_child(fruit)
			# Setting fruit scale based on relative size
			fruit.scale = Vector2(1, 1)
			# Setting position to not move from original position
			fruit.global_position = crate_position
			
	if success:
		var round_end_jsons: Array[String] = nar_manager.generate_simple_round_success(fruit_selector.fruit_singular, fruit_selector.fruit_plural, fruit_count)
		narrator.play_narration(caption_enabled, jsonFilePath, round_end_jsons, audioFilePath)
	else:
		var incorrect_jsons: Array[String] = nar_manager.generate_third_inccorect(fruit_count)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
		
	anim_player.play("remove_crate")

func validate_count() -> bool:
	var count: int = 0
	
	for fruit in fruit_array:
		if fruit.in_crate:
			count +=1
	
	return count == fruit_count

func _on_animation_player_animation_finished(anim_name: StringName) -> void:
	if round_manager.round_number <= 3:
		if anim_name == "intro" || anim_name == "show_crates":
			spawn_fruit()
		elif anim_name == "remove_crate":
			reset()
			set_round()
	else:
		if anim_name != "show_end":
			var game_end_jsons: Array[String] = nar_manager.generate_simple_outro(fruit_selector.fruit_singular)
			narrator.play_narration(caption_enabled, jsonFilePath, game_end_jsons, audioFilePath)
			anim_player.play("show_end")
		else:
			Globals.game_scene = "res://levels/fruit_games/fruit_stands/variations/normal_fruit_stand/normal_fruit_stand.tscn"
			var scene: Resource = load("res://levels/fruit_games/play_again/fruit_play_again.tscn")
			if scene:
				get_tree().change_scene_to_packed(scene)
			else:
				print("Failed to load scene!")

func _on_ok_button_clicked() -> void:
	var is_valid: bool = validate_count()
	
	if is_valid:
		end_round(true)
	else:
		handle_invalid()


func _on_help_button_clicked() -> void:
	var help_jsons: Array[String] = nar_manager.generate_simple_help(fruit_selector.fruit_plural)
	narrator.play_narration(caption_enabled, jsonFilePath, help_jsons, audioFilePath)


func _on_caption_button_clicked() -> void:
	caption_enabled = !caption_enabled
	caption_button.hasCC = caption_enabled
