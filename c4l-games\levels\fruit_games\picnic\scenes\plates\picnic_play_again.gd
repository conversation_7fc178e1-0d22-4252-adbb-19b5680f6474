extends Node2D

@onready var nar_manager = $PicnicNarrationManager
@onready var narrator = $Narrator

var audioFilePath: String = "res://levels/fruit_games/audio/"
var jsonFilePath: String = "res://levels/fruit_games/closed_caption_jsons/"

var caption_enabled: bool = false

func _ready():
	var play_again_jsons: Array[String] = ["question_again.json"]
	narrator.play_narration(caption_enabled, jsonFilePath, play_again_jsons, audioFilePath)
	pass

func _on_yes_button_clicked() -> void:
	#TODO Dynamically handle which level
	var scene = load("res://levels/fruit_games/picnic/2_plate_picnic.tscn")
	print(scene)
	if scene:
		get_tree().change_scene_to_packed(scene)
	else:
		print("Failed to load scene!")


func _on_no_button_clicked() -> void:
	var scene = load("res://levels/game_center/game_center.tscn")
	print(scene)
	if scene:
		get_tree().change_scene_to_packed(scene)
	else:
		print("Failed to load scene!")
