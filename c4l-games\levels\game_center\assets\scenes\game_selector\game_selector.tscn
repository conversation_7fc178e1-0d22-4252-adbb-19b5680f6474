[gd_scene load_steps=2 format=3 uid="uid://3f4bp6mnacax"]

[ext_resource type="Script" uid="uid://d2gybet70o4my" path="res://levels/game_center/assets/scenes/game_selector/game_selector.gd" id="1_twasf"]

[node name="GameSelector" type="Control"]
custom_minimum_size = Vector2(480, 360)
layout_mode = 3
anchors_preset = 0
offset_right = 480.0
offset_bottom = 360.0
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 1
mouse_force_pass_scroll_events = false
script = ExtResource("1_twasf")

[node name="PanelContainer" type="PanelContainer" parent="."]
custom_minimum_size = Vector2(200, 200)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 1

[node name="VBoxContainer" type="VBoxContainer" parent="PanelContainer"]
layout_mode = 2

[node name="MarginContainer" type="MarginContainer" parent="PanelContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 0
theme_override_constants/margin_left = 5
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 5
theme_override_constants/margin_bottom = 10

[node name="Label" type="Label" parent="PanelContainer/VBoxContainer/MarginContainer"]
layout_mode = 2
mouse_filter = 1
theme_override_font_sizes/font_size = 32

[node name="TextureRect" type="TextureRect" parent="PanelContainer/VBoxContainer"]
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
expand_mode = 5
stretch_mode = 4

[connection signal="gui_input" from="." to="." method="_on_gui_input"]
