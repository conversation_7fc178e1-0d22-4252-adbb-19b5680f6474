extends Node2D

@onready var round_manager = $RoundManager
@onready var example_plate = $ExamplePlate
@onready var placement_plate = $PlacementPlate
@onready var anim_player = $AnimationPlayer
@onready var label = $CanvasLayer/Panel/Label
@onready var nar_manager = $PicnicNarrationManager
@onready var narrator = $Narrator
@onready var help_button = $HelpButton
@onready var caption_button = $CaptionButton

var pre_load_apple_slice: PackedScene = preload("res://levels/fruit_games/picnic/scenes/slices/apple_slice.tscn")

var audioFilePath: String = "res://levels/fruit_games/audio/"
var jsonFilePath: String = "res://levels/fruit_games/closed_caption_jsons/"

var slice_array: Array[Node2D]
var slice_count: int
var caption_enabled: bool = false

var attempt: int

func _ready():
	example_plate.set_textures("res://levels/fruit_games/sprites/C4L-ORC-Picnic-slice_apple.png")
	help_button.sprite_2d.texture = load("res://levels/fruit_games/sprites/C4L-ORC-Help-Button.png")
	round_manager.new_round()
	slice_count = round_manager.slice_count
	label.text = str(slice_count)
	var intro_jsons: Array[String] = nar_manager.generate_intro("token_appleSlice.json", "token_appleSlice_plural.json", slice_count)
	narrator.play_narration(caption_enabled, jsonFilePath, intro_jsons, audioFilePath)
	
	attempt = 1
	anim_player.play("show_plates")

func reset() -> void:
	example_plate.reset()
	for slice in slice_array:
		slice.get_parent().remove_child(slice)
		slice.queue_free()
	slice_array.clear()

func set_round() -> void:
	attempt = 1
	round_manager.new_round()
	slice_count = round_manager.slice_count
	label.text = str(slice_count)
	
	if round_manager.round_number <= 3:
		var round_jsons: Array[String] = nar_manager.generate_round_intro("token_appleSlice.json", "token_appleSlice_plural.json", slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, round_jsons, audioFilePath)
	
	anim_player.play("show_plates")
	

func spawn_slice() -> void:
	example_plate.show_slices(slice_count)
	
	for i in range(slice_count+2):
		#TODO Make this generic to create any type of slice
		var slice: Node2D = pre_load_apple_slice.instantiate() as Node2D
		slice.position = $SliceSpawn.position
		#Shrinking relative size of slice
		slice.scale = Vector2(0.8, 0.8)
		add_child(slice)
		slice.connect("check_drop_area", check_slice_in_plate )
		slice_array.append(slice)
		slice.spawn_animation()
		await get_tree().create_timer(0.2).timeout

func check_slice_in_plate(slice: Slice) -> void:
	var plate_area = placement_plate.get_node("Area2D")
	var slice_area = slice.get_node("Area2D")
	slice.on_plate = plate_area.overlaps_area(slice_area)

func validate_slice_count() -> bool:
	var placed_slice_count: int = 0
	for slice in slice_array:
		if slice.on_plate:
			placed_slice_count += 1
	
	return placed_slice_count == slice_count
	
func end_round(success: bool) -> void:
	for slice in slice_array:
		slice.disabled = true
		if slice.on_plate:
			var plate_position = slice.global_position
			remove_child(slice)
			placement_plate.add_child(slice)
			# Setting slice scale based on relative size
			slice.scale = Vector2(1, 1)
			# Setting position to not move from original position
			slice.global_position = plate_position
	
	if success:
		var round_end_jsons: Array[String] = nar_manager.generate_round_end("token_appleSlice.json", "token_appleSlice_plural.json", slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, round_end_jsons, audioFilePath)
	else:
		var incorrect_jsons: Array[String] = nar_manager.generate_third_inccorect(slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	anim_player.play("remove_plates")

func handle_invalid() -> void:
	print(str(attempt))
	if attempt == 1:
		var incorrect_jsons: Array[String] = nar_manager.generate_first_inccorect("token_appleSlice_plural.json")
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	elif attempt == 2:
		var incorrect_jsons: Array[String] = nar_manager.generate_second_inccorect("token_appleSlice_plural.json", slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	else:
		end_round(false)
		
	attempt += 1

func _on_ok_button_clicked() -> void:
	var is_valid: bool = validate_slice_count()

	if is_valid:
		end_round(true)
	else:
		handle_invalid()

func _on_animation_player_animation_finished(anim_name: StringName) -> void:
	if round_manager.round_number <= 3:
		if anim_name == "show_plates":
			spawn_slice()
		elif anim_name == "remove_plates":
			reset()
			set_round()
	else:
		if anim_name != "show_end":
			var game_end_jsons: Array[String] = nar_manager.generate_game_end("token_appleSlice_plural.json")
			narrator.play_narration(caption_enabled, jsonFilePath, game_end_jsons, audioFilePath)
			anim_player.play("show_end")
		else:
			Globals.game_scene = "res://levels/fruit_games/picnic/2_plate_picnic.tscn"
			var scene: Resource = load("res://levels/fruit_games/play_again/fruit_play_again.tscn")
			if scene:
				get_tree().change_scene_to_packed(scene)
			else:
				print("Failed to load scene!")


func _on_caption_button_clicked() -> void:
	caption_enabled = !caption_enabled
	caption_button.hasCC = caption_enabled


func _on_help_button_clicked() -> void:
	var help_jsons: Array[String] = nar_manager.generate_help("token_appleSlice_plural.json")
	narrator.play_narration(caption_enabled, jsonFilePath, help_jsons, audioFilePath)


func _on_example_plate_click() -> void:
	var num_jsons: Array[String] = nar_manager.generate_plate_number(slice_count)
	narrator.play_narration(caption_enabled, jsonFilePath, num_jsons, audioFilePath)
