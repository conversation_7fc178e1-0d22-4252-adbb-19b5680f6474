[gd_scene load_steps=17 format=3 uid="uid://dw8fplqy2kkko"]

[ext_resource type="Texture2D" uid="uid://cabhahq50ini8" path="res://levels/fruit_games/sprites/C4L-ORC-Picnic-background.png" id="1_5aw55"]
[ext_resource type="Script" uid="uid://6xba32kygiet" path="res://levels/fruit_games/picnic/abstract_picnic.gd" id="1_j1waa"]
[ext_resource type="Texture2D" uid="uid://c26rheeq4tu0h" path="res://levels/fruit_games/sprites/C4L-ORC-Picnic-Napkin.png" id="2_pprhn"]
[ext_resource type="PackedScene" uid="uid://bgylp2ib1pmg1" path="res://levels/fruit_games/picnic/scenes/plates/placement_plate.tscn" id="3_5aw55"]
[ext_resource type="PackedScene" uid="uid://l2xlmyflv80p" path="res://generic/scenes/buttons/ok_button.tscn" id="4_hl0ct"]
[ext_resource type="PackedScene" uid="uid://c7gqb3n6djcea" path="res://levels/fruit_games/picnic/scenes/round_manager/round_manager.tscn" id="5_j1waa"]
[ext_resource type="PackedScene" uid="uid://cphnk1qp1ht7m" path="res://levels/fruit_games/picnic/scenes/narration_manager/picnic_narration_manager.tscn" id="6_bih6f"]
[ext_resource type="PackedScene" uid="uid://bw1hwysqa14ib" path="res://generic/scenes/narrator/narrator.tscn" id="7_hhl0r"]
[ext_resource type="PackedScene" uid="uid://cakvwk6r6comh" path="res://generic/scenes/buttons/help_button/help_button.tscn" id="8_5ddag"]
[ext_resource type="PackedScene" uid="uid://bh58tl2wuwhc4" path="res://generic/scenes/buttons/caption_button/caption_button.tscn" id="9_j8mc8"]
[ext_resource type="PackedScene" uid="uid://cmevafaqxiiq4" path="res://levels/fruit_games/picnic/scenes/fruit_slice_selector/fruit_slice_selector.tscn" id="11_bih6f"]

[sub_resource type="Animation" id="Animation_3ui5w"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlacementPlate:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1000, -450)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("CanvasLayer/Panel/Label:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CanvasLayer/Panel/Label:theme_override_font_sizes/font_size")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [512]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("CanvasLayer/Panel/Label:text")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [""]
}

[sub_resource type="Animation" id="Animation_j6m5h"]
resource_name = "remove_plates"
length = 8.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlacementPlate:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2, 2.4, 2.6, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(960, 400), Vector2(960, 400), Vector2(910, 400), Vector2(1060, 400), Vector2(2500, 400)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 4, 4.1, 7.8, 8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [false, false, true, true, false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("CanvasLayer/Panel/Label:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(4, 6, 8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CanvasLayer/Panel/Label:theme_override_font_sizes/font_size")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(4, 6, 8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [1024, 512, 1024]
}

[sub_resource type="Animation" id="Animation_bxvq8"]
resource_name = "show_end"
length = 4.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Panel/Label:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("CanvasLayer/Panel/Label:theme_override_font_sizes/font_size")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [512, 256]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CanvasLayer/Panel/Label:text")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": ["Great Job!"]
}

[sub_resource type="Animation" id="Animation_5v4d0"]
resource_name = "show_plates"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("PlacementPlate:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(960, -450), Vector2(960, 400)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_3ui5w"]
_data = {
&"RESET": SubResource("Animation_3ui5w"),
&"remove_plates": SubResource("Animation_j6m5h"),
&"show_end": SubResource("Animation_bxvq8"),
&"show_plates": SubResource("Animation_5v4d0")
}

[node name="AbstractPicnic" type="Node2D"]
script = ExtResource("1_j1waa")

[node name="Background" type="Node2D" parent="."]

[node name="Blanket" type="Sprite2D" parent="Background"]
position = Vector2(965, 550)
scale = Vector2(0.85, 0.8)
texture = ExtResource("1_5aw55")

[node name="Napkin" type="Sprite2D" parent="Background"]
texture_filter = 1
position = Vector2(959, 910)
scale = Vector2(0.8, 0.7)
texture = ExtResource("2_pprhn")
hframes = 3

[node name="Fruit" type="Sprite2D" parent="Background"]
position = Vector2(1000, 930)
scale = Vector2(0.8, 0.8)
hframes = 2
frame = 1

[node name="PlacementPlate" parent="." instance=ExtResource("3_5aw55")]
position = Vector2(1000, -450)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_3ui5w")
}

[node name="SliceSpawn" type="Marker2D" parent="."]
position = Vector2(1078, 1008)

[node name="OkButton" parent="." instance=ExtResource("4_hl0ct")]
position = Vector2(1700, 950)

[node name="RoundManager" parent="." instance=ExtResource("5_j1waa")]
position = Vector2(1700, 950)

[node name="CanvasLayer" type="CanvasLayer" parent="."]
visible = false

[node name="Panel" type="Panel" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Label" type="Label" parent="CanvasLayer/Panel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -73.0
offset_top = -174.5
offset_right = 73.0
offset_bottom = 174.5
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 512
horizontal_alignment = 1

[node name="PicnicNarrationManager" parent="." instance=ExtResource("6_bih6f")]

[node name="Narrator" parent="." instance=ExtResource("7_hhl0r")]

[node name="HelpButton" parent="." instance=ExtResource("8_5ddag")]
position = Vector2(65, 65)

[node name="CaptionButton" parent="." instance=ExtResource("9_j8mc8")]
position = Vector2(60, 175)

[node name="PlacementMarker2D" type="Marker2D" parent="."]
position = Vector2(960, 400)

[node name="FruitSliceSelector" parent="." instance=ExtResource("11_bih6f")]
position = Vector2(100, 892)

[node name="DisplayInfo" type="CanvasLayer" parent="."]

[node name="MarginContainer" type="MarginContainer" parent="DisplayInfo"]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_top = -225.0
offset_right = 123.0
grow_vertical = 0
theme_override_constants/margin_left = 100
theme_override_constants/margin_bottom = 0

[node name="SliceCountLabel" type="Label" parent="DisplayInfo/MarginContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0.835294, 1)
theme_override_font_sizes/font_size = 128
text = "0"

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_animation_player_animation_finished"]
[connection signal="button_clicked" from="OkButton" to="." method="_on_ok_button_clicked"]
[connection signal="help_clicked" from="HelpButton" to="." method="_on_help_button_clicked"]
[connection signal="caption_clicked" from="CaptionButton" to="." method="_on_caption_button_clicked"]
