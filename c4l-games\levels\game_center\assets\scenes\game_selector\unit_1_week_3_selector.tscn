[gd_scene load_steps=3 format=3 uid="uid://ddr16u6xeh7uq"]

[ext_resource type="PackedScene" uid="uid://3f4bp6mnacax" path="res://levels/game_center/assets/scenes/game_selector/game_selector.tscn" id="1_nj8tg"]
[ext_resource type="Texture2D" uid="uid://c0otta2ml153j" path="res://levels/game_center/assets/images/C4L-Verbal-Counting.png" id="2_useeh"]

[node name="Unit1Week3Selector" instance=ExtResource("1_nj8tg")]
game_scene = "res://levels/counting_games/get_on_bus/get_on_bus.tscn"
unit = 1
week = 3

[node name="Label" parent="PanelContainer/VBoxContainer/MarginContainer" index="0"]
text = "Unit1, Week 3: Get On Bus"

[node name="TextureRect" parent="PanelContainer/VBoxContainer" index="1"]
texture = ExtResource("2_useeh")
