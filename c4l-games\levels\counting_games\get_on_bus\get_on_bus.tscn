[gd_scene load_steps=12 format=3 uid="uid://c0i61hw4mjvq2"]

[ext_resource type="Script" uid="uid://bljc0sdbq0v6a" path="res://levels/counting_games/get_on_bus/get_on_bus.gd" id="1_n4hkg"]
[ext_resource type="Texture2D" uid="uid://exdbemiht5de" path="res://levels/counting_games/sprites/sky.png" id="2_w61pa"]
[ext_resource type="PackedScene" uid="uid://bx5iw8ee0rmrm" path="res://levels/counting_games/general/scenes/count_cloud/count_cloud.tscn" id="3_0otet"]
[ext_resource type="PackedScene" uid="uid://dexurfgq5qkmx" path="res://levels/counting_games/get_on_bus/scenes/school/school.tscn" id="4_tvtcy"]
[ext_resource type="PackedScene" uid="uid://7du5s4nx71fm" path="res://levels/counting_games/get_on_bus/scenes/crossing_guard/crossing_guard.tscn" id="5_keb86"]
[ext_resource type="Texture2D" uid="uid://7v4t17i3m387" path="res://levels/counting_games/sprites/lot.png" id="6_o3bgl"]
[ext_resource type="PackedScene" uid="uid://o0xuwjbg5o4" path="res://levels/counting_games/general/scenes/bus/bus.tscn" id="7_bype0"]
[ext_resource type="PackedScene" uid="uid://cakvwk6r6comh" path="res://generic/scenes/buttons/help_button/help_button.tscn" id="8_71x4a"]
[ext_resource type="PackedScene" uid="uid://bh58tl2wuwhc4" path="res://generic/scenes/buttons/caption_button/caption_button.tscn" id="9_40w0s"]
[ext_resource type="AudioStream" uid="uid://cd8wwxemhe2kw" path="res://levels/counting_games/general/audio/bus-fade.wav" id="10_sib83"]
[ext_resource type="PackedScene" uid="uid://bw1hwysqa14ib" path="res://generic/scenes/narrator/narrator.tscn" id="11_d1wv7"]

[node name="GetOnBus" type="Node2D"]
script = ExtResource("1_n4hkg")

[node name="Background" type="Node2D" parent="."]
scale = Vector2(0.85, 0.75)

[node name="Sky" type="Sprite2D" parent="Background"]
texture = ExtResource("2_w61pa")
centered = false

[node name="CountCloud" parent="Background" instance=ExtResource("3_0otet")]
position = Vector2(962.745, 209.901)

[node name="School" parent="Background" instance=ExtResource("4_tvtcy")]
position = Vector2(436, 235)

[node name="CrossingGuard" parent="Background" instance=ExtResource("5_keb86")]
position = Vector2(1975, 350)

[node name="ParkingLot" type="Sprite2D" parent="Background"]
texture = ExtResource("6_o3bgl")
centered = false

[node name="Bus1" parent="Background" instance=ExtResource("7_bype0")]
position = Vector2(200, 950)
scale = Vector2(1, 1.01)

[node name="Bus2" parent="Background" instance=ExtResource("7_bype0")]
position = Vector2(675, 950)
scale = Vector2(1, 1.01)

[node name="Bus3" parent="Background" instance=ExtResource("7_bype0")]
position = Vector2(1150, 950)
scale = Vector2(1, 1.01)

[node name="Bus4" parent="Background" instance=ExtResource("7_bype0")]
position = Vector2(1600, 950)
scale = Vector2(1, 1.01)

[node name="Bus5" parent="Background" instance=ExtResource("7_bype0")]
position = Vector2(2050, 950)
scale = Vector2(1, 1.01)

[node name="HelpButton" parent="." instance=ExtResource("8_71x4a")]
position = Vector2(33, 29)
scale = Vector2(0.51, 0.45)

[node name="CaptionButton" parent="." instance=ExtResource("9_40w0s")]
position = Vector2(28, 85)

[node name="Timer" type="Timer" parent="."]
wait_time = 5.0
one_shot = true

[node name="BusDriveAudio" type="AudioStreamPlayer" parent="."]
stream = ExtResource("10_sib83")

[node name="Narrator" parent="." instance=ExtResource("11_d1wv7")]
position = Vector2(1328, 96)

[connection signal="school_clicked" from="Background/School" to="." method="_on_school_school_clicked"]
[connection signal="on_guard_click" from="Background/CrossingGuard" to="." method="_on_crossing_guard_on_guard_click"]
[connection signal="help_clicked" from="HelpButton" to="." method="_on_help_button_help_clicked"]
[connection signal="caption_clicked" from="CaptionButton" to="." method="_on_caption_button_caption_clicked"]
[connection signal="timeout" from="Timer" to="." method="_on_timer_timeout"]
