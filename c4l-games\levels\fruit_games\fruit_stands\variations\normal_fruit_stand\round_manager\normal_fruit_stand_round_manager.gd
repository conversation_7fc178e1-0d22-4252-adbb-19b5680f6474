extends Node2D

var round_number: int
var fruit_count: int
var fruit_start: int

func _ready():
	randomize()  # Seed the random number generator
	round_number = 0
	fruit_count = 0
	fruit_start = 0
	pass

func new_round(has_starting_fruit: bool, difficulty: int) -> void:
	round_number+=1
	
	match round_number:
		1:
			if difficulty == 0:
				fruit_count = randi_range(1, 2)
			else:
				fruit_count = randi_range(2, 4)
		2:
			if difficulty == 0:
				fruit_count = randi_range(3, 4)
			else:
				fruit_count = randi_range(5, 9)
		3:
			if difficulty == 0:
				fruit_count = 5
			else:
				fruit_count = 10
			
	if has_starting_fruit:
		if fruit_count < 3:
			fruit_start = 1
		else:
			fruit_start = randi_range(1, fruit_count - 2)
	else:
		fruit_start = 0
