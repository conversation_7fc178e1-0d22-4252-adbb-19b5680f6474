[gd_scene load_steps=3 format=3 uid="uid://cb7g6pn3dquh"]

[ext_resource type="PackedScene" uid="uid://8hvshnm2lvd" path="res://levels/counting_games/general/scenes/students/standing_students/standing_student.tscn" id="1_j75dj"]
[ext_resource type="Texture2D" uid="uid://dyeqw2boanrgi" path="res://levels/counting_games/sprites/student1-stand.png" id="2_5utyq"]

[node name="BlueStandingStudent" instance=ExtResource("1_j75dj")]

[node name="Sprite2D" parent="." index="0"]
texture = ExtResource("2_5utyq")

[node name="CollisionShape2D" parent="Area2D" index="0"]
position = Vector2(0, 2)
