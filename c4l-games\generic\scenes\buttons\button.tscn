[gd_scene load_steps=3 format=3 uid="uid://r7yrgcsmo7mx"]

[ext_resource type="Script" uid="uid://8er4igakovbr" path="res://generic/scenes/buttons/button.gd" id="1_md2mq"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_lia7o"]
size = Vector2(225, 173)

[node name="Button" type="Node2D"]
script = ExtResource("1_md2mq")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
hframes = 4

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("RectangleShape2D_lia7o")

[connection signal="input_event" from="Area2D" to="." method="_on_area_2d_input_event"]
[connection signal="mouse_entered" from="Area2D" to="." method="_on_area_2d_mouse_entered"]
[connection signal="mouse_exited" from="Area2D" to="." method="_on_area_2d_mouse_exited"]
