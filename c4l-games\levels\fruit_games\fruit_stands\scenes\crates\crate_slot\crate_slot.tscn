[gd_scene load_steps=5 format=3 uid="uid://k5u0d1i50qj4"]

[ext_resource type="Texture2D" uid="uid://chxnj2dhjloun" path="res://levels/fruit_games/sprites/C4L-ORC-Stand-slot.png" id="1_7ymdm"]
[ext_resource type="Script" uid="uid://buiwkioyncble" path="res://levels/fruit_games/fruit_stands/scenes/crates/crate_slot/crate_slot.gd" id="1_ulfxg"]

[sub_resource type="Animation" id="Animation_7ymdm"]
resource_name = "spawn_slot"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(2, 2), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite2D:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Sprite2D:frame")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [1, 0]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Sprite2D:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(0, -100), Vector2(0, 0)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Sprite2D:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_ulfxg"]
_data = {
&"spawn_slot": SubResource("Animation_7ymdm")
}

[node name="CrateSlot" type="Node2D"]
script = ExtResource("1_ulfxg")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_7ymdm")
hframes = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_ulfxg")
}
