[gd_scene load_steps=4 format=3 uid="uid://dexurfgq5qkmx"]

[ext_resource type="Script" uid="uid://bqpmd4tfkfr32" path="res://assets/scenes/school/school.gd" id="1_xnh6d"]
[ext_resource type="Texture2D" uid="uid://civmloypqkuv5" path="res://assets/sprites/school.png" id="1_ydosa"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_xnh6d"]
size = Vector2(392, 383)

[node name="School" type="Node2D"]
script = ExtResource("1_xnh6d")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("1_ydosa")
hframes = 4

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(-2, -3.5)
shape = SubResource("RectangleShape2D_xnh6d")

[connection signal="input_event" from="Area2D" to="." method="_on_area_2d_input_event"]
[connection signal="mouse_entered" from="Area2D" to="." method="_on_area_2d_mouse_entered"]
[connection signal="mouse_exited" from="Area2D" to="." method="_on_area_2d_mouse_exited"]
