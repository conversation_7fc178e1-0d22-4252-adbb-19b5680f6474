[gd_scene load_steps=18 format=3 uid="uid://bqt30qt8jlp0y"]

[ext_resource type="Texture2D" uid="uid://tbtvorbfimsl" path="res://levels/fruit_games/sprites/C4L-ORC-Stand-background.png" id="1_ljnel"]
[ext_resource type="Script" uid="uid://cy8bkl5okbb2m" path="res://levels/fruit_games/fruit_stands/variations/normal_fruit_stand/normal_fruit_stand.gd" id="1_ryl1t"]
[ext_resource type="PackedScene" uid="uid://bdwgqd4532nxg" path="res://levels/fruit_games/fruit_stands/scenes/crates/single_crate/single_crate.tscn" id="2_0fhn2"]
[ext_resource type="PackedScene" uid="uid://7df4pf3orom2" path="res://levels/fruit_games/fruit_stands/scenes/crates/large_crate/large_crate.tscn" id="3_rgxwf"]
[ext_resource type="PackedScene" uid="uid://l2xlmyflv80p" path="res://generic/scenes/buttons/ok_button.tscn" id="4_i5y14"]
[ext_resource type="PackedScene" uid="uid://dvrrefnjurujr" path="res://levels/fruit_games/fruit_stands/scenes/fruit_selector/fruit_selector.tscn" id="6_lb22a"]
[ext_resource type="PackedScene" uid="uid://bag4i4uy67y8i" path="res://levels/fruit_games/fruit_stands/scenes/fruit_stand_narration_manager/fruit_stand_narration_manager.tscn" id="8_7cui4"]
[ext_resource type="PackedScene" uid="uid://bw1hwysqa14ib" path="res://generic/scenes/narrator/narrator.tscn" id="9_bk7ua"]
[ext_resource type="PackedScene" uid="uid://cakvwk6r6comh" path="res://generic/scenes/buttons/help_button/help_button.tscn" id="10_wuydo"]
[ext_resource type="PackedScene" uid="uid://bh58tl2wuwhc4" path="res://generic/scenes/buttons/caption_button/caption_button.tscn" id="11_bgm0g"]
[ext_resource type="PackedScene" uid="uid://cjxssd12n5j23" path="res://levels/fruit_games/fruit_stands/variations/normal_fruit_stand/round_manager/normal_fruit_stand_round_manager.tscn" id="11_ryl1t"]

[sub_resource type="Animation" id="Animation_lb22a"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LargeCrate:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1000, 450)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SingleCrate:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(250, 500)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("OkButton:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1000, 986)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CanvasLayer:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_3rur0"]
resource_name = "intro"
length = 1.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LargeCrate:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1000, -500), Vector2(1000, 450)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SingleCrate:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(1, 1.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-150, 500), Vector2(250, 500)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("OkButton:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(1, 1.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1000, 1500), Vector2(1000, 986)]
}

[sub_resource type="Animation" id="Animation_7cui4"]
resource_name = "remove_crate"
length = 6.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LargeCrate:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.22, 2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1000, 450), Vector2(800, 450), Vector2(800, 450), Vector2(2500, 450)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 2, 2.1, 5.8, 6),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [false, false, true, true, false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("CanvasLayer/Panel/Label:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(2, 4, 6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CanvasLayer/Panel/Label:theme_override_font_sizes/font_size")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(2, 4, 6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [1024, 512, 1024]
}

[sub_resource type="Animation" id="Animation_7uoae"]
resource_name = "show_crates"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LargeCrate:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1000, -500), Vector2(1000, 450)]
}

[sub_resource type="Animation" id="Animation_bk7ua"]
resource_name = "show_end"
length = 4.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Panel/Label:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("CanvasLayer/Panel/Label:theme_override_font_sizes/font_size")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [512, 256]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CanvasLayer/Panel/Label:text")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": ["Great Job!"]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1ffdo"]
_data = {
&"RESET": SubResource("Animation_lb22a"),
&"intro": SubResource("Animation_3rur0"),
&"remove_crate": SubResource("Animation_7cui4"),
&"show_crates": SubResource("Animation_7uoae"),
&"show_end": SubResource("Animation_bk7ua")
}

[node name="NormalFruitStand" type="Node2D"]
script = ExtResource("1_ryl1t")

[node name="Background" type="Node2D" parent="."]

[node name="Sprite2D" type="Sprite2D" parent="Background"]
position = Vector2(960, 550)
scale = Vector2(0.85, 0.8)
texture = ExtResource("1_ljnel")

[node name="SingleCrate" parent="." instance=ExtResource("2_0fhn2")]
position = Vector2(250, 500)
scale = Vector2(0.8, 0.8)

[node name="LargeCrate" parent="." instance=ExtResource("3_rgxwf")]
position = Vector2(1000, 450)
scale = Vector2(0.8, 0.8)

[node name="OkButton" parent="." instance=ExtResource("4_i5y14")]
position = Vector2(1000, 986)
scale = Vector2(0.8, 0.8)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1ffdo")
}

[node name="FruitSelector" parent="." instance=ExtResource("6_lb22a")]

[node name="CanvasLayer" type="CanvasLayer" parent="."]
visible = false

[node name="Panel" type="Panel" parent="CanvasLayer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Label" type="Label" parent="CanvasLayer/Panel"]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -22.5
offset_right = 20.0
offset_bottom = 22.5
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 512
text = "Great Job!"
horizontal_alignment = 1

[node name="FruitStandNarrationManager" parent="." instance=ExtResource("8_7cui4")]

[node name="Narrator" parent="." instance=ExtResource("9_bk7ua")]

[node name="HelpButton" parent="." instance=ExtResource("10_wuydo")]
position = Vector2(55, 39)

[node name="CaptionButton" parent="." instance=ExtResource("11_bgm0g")]
position = Vector2(50, 150)

[node name="CanvasLayer2" type="CanvasLayer" parent="."]

[node name="MarginContainer" type="MarginContainer" parent="CanvasLayer2"]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_top = -129.0
offset_right = 116.0
grow_vertical = 0

[node name="FruitCountLabel" type="Label" parent="CanvasLayer2/MarginContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 0, 0.823529, 1)
theme_override_font_sizes/font_size = 128
text = "10"

[node name="NormalFruitStandRoundManager" parent="." instance=ExtResource("11_ryl1t")]

[connection signal="button_clicked" from="OkButton" to="." method="_on_ok_button_clicked"]
[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_animation_player_animation_finished"]
[connection signal="help_clicked" from="HelpButton" to="." method="_on_help_button_clicked"]
[connection signal="caption_clicked" from="CaptionButton" to="." method="_on_caption_button_clicked"]
