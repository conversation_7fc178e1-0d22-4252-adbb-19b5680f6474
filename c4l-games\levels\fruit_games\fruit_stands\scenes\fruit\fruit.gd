extends Node2D
class_name Fruit

signal check_drop_area(fruit_sender)

@onready var anim_player = $AnimationPlayer
@onready var sprite = $Sprite2D

var dragging: bool = false
var in_crate: bool = false
var crate_index: int = -1
var disabled: bool = false
var original_position: Vector2 = Vector2()
var start_position: Vector2 = Vector2()  # Store the initial starting position
var velocity: Vector2 = Vector2()
var spring_strength: float = 10.0
var damping: float = 0.8

func _ready():
	disabled = false
	in_crate = false
	original_position = global_position
	start_position = global_position  # Save the initial starting position

func _process(delta):
	if !disabled:
		if dragging:
			sprite.frame = 1
			in_crate = false
			global_position = get_global_mouse_position()
			velocity = Vector2.ZERO
		elif !in_crate:
			sprite.frame = 0
			# Spring physics for elastic return when not in valid position
			var direction: Vector2 = original_position - global_position
			
			# If we're close enough to home position, just snap to it
			if direction.length() < 1.0:
				global_position = original_position
				velocity = Vector2.ZERO
			else:
				var spring_force: Vector2 = direction * spring_strength
				
				velocity += spring_force * delta
				velocity *= damping
				
				global_position += velocity

func check_valid_drop_area() -> void:
	# Send signal to check if fruit is in crate
	check_drop_area.emit(self)

func _on_area_2d_input_event(_viewport: Node, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			if event.pressed:
				# Start dragging and move this fruit to the front
				dragging = true
				# Move this node to the end of its parent's children list (top of visual stack)
				var parent = get_parent()
				parent.move_child(self, parent.get_child_count() - 1)
				# Prevent event from propagating to fruits below
				get_viewport().set_input_as_handled()
			else:
				# Stop dragging and check if in valid area
				dragging = false
				check_valid_drop_area()
				
				if not in_crate:
					# Not in valid area, return to starting position
					original_position = start_position
				else:
					# In valid area, keep it where it is
					original_position = global_position

func spawn_animation() -> void:
	anim_player.play("spawn_fruit")
	
