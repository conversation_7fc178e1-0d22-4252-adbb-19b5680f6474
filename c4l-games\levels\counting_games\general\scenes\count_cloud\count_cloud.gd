extends Node2D

var count: int = 0
var count_limit: int = 50
var count_array: Array[String]

func _ready():
	var number_words = [
		"one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten",
		"eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen", "twenty",
		"twenty-one", "twenty-two", "twenty-three", "twenty-four", "twenty-five", "twenty-six", "twenty-seven", "twenty-eight", "twenty-nine", "thirty",
		"thirty-one", "thirty-two", "thirty-three", "thirty-four", "thirty-five", "thirty-six", "thirty-seven", "thirty-eight", "thirty-nine", "forty",
		"forty-one", "forty-two", "forty-three", "forty-four", "forty-five", "forty-six", "forty-seven", "forty-eight", "forty-nine", "fifty"
	]
	
	count_array = []
	for word in number_words:
		var audio_path = word + ".json"
		count_array.append(audio_path)

func count_next() -> void:
	if count < count_limit:
		count+=1
		$Label.text = str(count)

func reset_count():
	count = 0
	$Label.text = str(count)

func get_count() -> int:
	return count

func get_count_audio() -> String:
	var countAudio : String = count_array[count - 1]
	
	if count_array.size() >= count - 1:
		countAudio = count_array[count - 1]
	
	return countAudio
