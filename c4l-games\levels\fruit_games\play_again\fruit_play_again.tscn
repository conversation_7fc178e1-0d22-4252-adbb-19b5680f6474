[gd_scene load_steps=7 format=3 uid="uid://bnoklllr7g6os"]

[ext_resource type="Script" uid="uid://bni302g8xdfxu" path="res://levels/fruit_games/play_again/fruit_play_again.gd" id="1_f1qjx"]
[ext_resource type="Texture2D" uid="uid://cx7vcqp2ifctw" path="res://levels/fruit_games/sprites/C4L-ORC-Loading-background.png" id="1_owwn6"]
[ext_resource type="PackedScene" uid="uid://br52juq1eqdp5" path="res://generic/scenes/buttons/yes_button.tscn" id="2_w42rd"]
[ext_resource type="PackedScene" uid="uid://b6dvktjbmab1f" path="res://generic/scenes/buttons/no_button.tscn" id="3_f1qjx"]
[ext_resource type="PackedScene" uid="uid://cphnk1qp1ht7m" path="res://levels/fruit_games/picnic/scenes/narration_manager/picnic_narration_manager.tscn" id="5_e24bo"]
[ext_resource type="PackedScene" uid="uid://bw1hwysqa14ib" path="res://generic/scenes/narrator/narrator.tscn" id="6_5jixt"]

[node name="PicnicPlayAgain" type="Node2D"]
script = ExtResource("1_f1qjx")

[node name="Sprite2D" type="Sprite2D" parent="."]
position = Vector2(932, 533)
texture = ExtResource("1_owwn6")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Control" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="Label" type="Label" parent="CanvasLayer/Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -22.5
offset_right = 20.0
offset_bottom = 22.5
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0, 0, 0.556863, 1)
theme_override_font_sizes/font_size = 126
text = "Play Again?"

[node name="YesButton" parent="." instance=ExtResource("2_w42rd")]
position = Vector2(800, 800)

[node name="NoButton" parent="." instance=ExtResource("3_f1qjx")]
position = Vector2(1100, 800)

[node name="PicnicNarrationManager" parent="." instance=ExtResource("5_e24bo")]

[node name="Narrator" parent="." instance=ExtResource("6_5jixt")]

[connection signal="button_clicked" from="YesButton" to="." method="_on_yes_button_clicked"]
[connection signal="button_clicked" from="NoButton" to="." method="_on_no_button_clicked"]
