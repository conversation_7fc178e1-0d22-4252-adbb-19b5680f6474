extends Node2D

signal example_plate_click()

var slice_array: Array[Sprite2D]

func _ready():
	slice_array = [$SliceSprite1, $SliceSprite2, $SliceSprite3, $SliceSprite4, $SliceSprite5]
	reset()

func set_textures(slice_sprite: String) -> void:
	for slice in slice_array:
		slice.texture = load(slice_sprite)

func show_slices(slice_count: int) -> void:
	slice_array.shuffle()
	var shown_slices: Array[Sprite2D] = slice_array.slice(0, slice_count)
	
	for slice in shown_slices:
		slice.visible = true

func reset() -> void:
	for slice in slice_array:
		slice.visible = false


func _on_area_2d_input_event(_viewport: Node, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton:
		if event.pressed:
			example_plate_click.emit();
