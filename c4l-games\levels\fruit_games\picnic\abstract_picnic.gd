extends Node2D

@onready var round_manager = $RoundManager
@onready var placement_plate = $PlacementPlate
@onready var anim_player = $AnimationPlayer
@onready var label = $CanvasLayer/Panel/Label
@onready var nar_manager = $PicnicNarrationManager
@onready var narrator = $Narrator
@onready var help_button = $HelpButton
@onready var caption_button = $CaptionButton
@onready var slice_count_label = $DisplayInfo/MarginContainer/SliceCountLabel
@onready var fruit_slice_selector = $FruitSliceSelector
@onready var slice_swan = $SliceSpawn
@onready var main_fruit = $Background/Fruit

var audioFilePath: String = "res://levels/fruit_games/audio/"
var jsonFilePath: String = "res://levels/fruit_games/closed_caption_jsons/"

var slice_array: Array[Node2D]
var slice_count: int
var caption_enabled: bool = false

var attempt: int

func select_fruit() -> void:
	if Globals.unit == 1 && Globals.week == 5:
		fruit_slice_selector.set_apple()
	elif Globals.unit == 2 && Globals.week == 2:
		fruit_slice_selector.set_grapefruit()
	elif Globals.unit == 4 && Globals.week == 1:
		fruit_slice_selector.set_orange()
	elif Globals.unit == 4 && Globals.week == 6:
		fruit_slice_selector.set_kiwi()
	else:
		fruit_slice_selector.set_grapefruit()

func _ready():
	select_fruit()
	
	main_fruit.texture = load(fruit_slice_selector.fruit_image)
	help_button.sprite_2d.texture = load("res://levels/fruit_games/sprites/C4L-ORC-Help-Button.png")
	round_manager.new_round()
	slice_count = round_manager.slice_count
	slice_count_label.text = str(slice_count)
	label.text = str(slice_count)
	var intro_jsons: Array[String] = nar_manager.generate_intro(fruit_slice_selector.fruit_singular, fruit_slice_selector.fruit_plural, slice_count)
	narrator.play_narration(caption_enabled, jsonFilePath, intro_jsons, audioFilePath)
	
	attempt = 1
	anim_player.play("show_plates")

func reset() -> void:
	for slice in slice_array:
		slice.get_parent().remove_child(slice)
		slice.queue_free()
	slice_array.clear()

func set_round() -> void:
	attempt = 1
	round_manager.new_round()
	slice_count = round_manager.slice_count
	slice_count_label.text = str(slice_count)
	label.text = str(slice_count)
	
	if round_manager.round_number <= 3:
		var round_jsons: Array[String] = nar_manager.generate_round_intro(fruit_slice_selector.fruit_singular, fruit_slice_selector.fruit_plural, slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, round_jsons, audioFilePath)
	
	anim_player.play("show_plates")
	
func spawn_slice() -> void:
	for i in range(slice_count+2):
		var slice: Node2D = fruit_slice_selector.pre_load_slice.instantiate() as Node2D
		
		slice.position = slice_swan.position
		#Shrinking relative size of slice
		slice.scale = Vector2(0.8, 0.8)
		add_child(slice)
		slice.connect("check_drop_area", check_slice_in_plate )
		slice_array.append(slice)
		slice.spawn_animation()
		await get_tree().create_timer(0.2).timeout

func check_slice_in_plate(slice: Slice) -> void:
	var plate_area = placement_plate.get_node("Area2D")
	var slice_area = slice.get_node("Area2D")
	slice.on_plate = plate_area.overlaps_area(slice_area)

func validate_slice_count() -> bool:
	var placed_slice_count: int = 0
	for slice in slice_array:
		if slice.on_plate:
			placed_slice_count += 1
	
	return placed_slice_count == slice_count

func end_round(success: bool) -> void:
	for slice in slice_array:
		slice.disabled = true
		if slice.on_plate:
			var plate_position = slice.global_position
			remove_child(slice)
			placement_plate.add_child(slice)
			# Setting slice scale based on relative size
			slice.scale = Vector2(1, 1)
			# Setting position to not move from original position
			slice.global_position = plate_position
	
	if success:
		var round_end_jsons: Array[String] = nar_manager.generate_round_end(fruit_slice_selector.fruit_singular, fruit_slice_selector.fruit_plural, slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, round_end_jsons, audioFilePath)
	else:
		var incorrect_jsons: Array[String] = nar_manager.generate_third_inccorect(slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	anim_player.play("remove_plates")

func handle_invalid() -> void:
	if attempt == 1:
		var incorrect_jsons: Array[String] = nar_manager.generate_first_inccorect(fruit_slice_selector.fruit_plural)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	elif attempt == 2:
		var incorrect_jsons: Array[String] = nar_manager.generate_second_inccorect(fruit_slice_selector.fruit_plural, slice_count)
		narrator.play_narration(caption_enabled, jsonFilePath, incorrect_jsons, audioFilePath)
	else:
		end_round(false)
		
	attempt += 1


func _on_ok_button_clicked() -> void:
	var is_valid: bool = validate_slice_count()

	if is_valid:
		end_round(true)
	else:
		handle_invalid()
		
func _on_animation_player_animation_finished(anim_name: StringName) -> void:
	if round_manager.round_number <= 3:
		if anim_name == "show_plates":
			spawn_slice()
		elif anim_name == "remove_plates":
			reset()
			set_round()
	else:
		if anim_name != "show_end":
			var game_end_jsons: Array[String] = nar_manager.generate_game_end(fruit_slice_selector.fruit_plural)
			narrator.play_narration(caption_enabled, jsonFilePath, game_end_jsons, audioFilePath)
			anim_player.play("show_end")
		else:
			Globals.game_scene = "res://levels/fruit_games/picnic/abstract_picnic.tscn"
			var scene: Resource = load("res://levels/fruit_games/play_again/fruit_play_again.tscn")
			if scene:
				get_tree().change_scene_to_packed(scene)
			else:
				print("Failed to load scene!")

func _on_help_button_clicked() -> void:
	var help_jsons: Array[String] = nar_manager.generate_help(fruit_slice_selector.fruit_plural)
	narrator.play_narration(caption_enabled, jsonFilePath, help_jsons, audioFilePath)

func _on_caption_button_clicked() -> void:
	caption_enabled = !caption_enabled
	caption_button.hasCC = caption_enabled
