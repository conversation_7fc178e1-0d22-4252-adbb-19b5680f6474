extends Node2D

signal help_clicked

@onready var sprite_2d = $Sprite2D

func _on_area_2d_mouse_entered() -> void:
	sprite_2d.frame = 1


func _on_area_2d_mouse_exited() -> void:
	sprite_2d.frame = 0


func _on_area_2d_input_event(_viewport: Node, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton:
		if event.pressed:
			sprite_2d.frame = 2
			help_clicked.emit()
		else:
			sprite_2d.frame = 1
