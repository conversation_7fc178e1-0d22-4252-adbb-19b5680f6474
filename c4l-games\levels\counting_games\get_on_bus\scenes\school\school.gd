extends Node2D

signal school_clicked

var enabled: bool = true

func _on_area_2d_mouse_entered() -> void:
	if enabled:
		$Sprite2D.frame = 1


func _on_area_2d_mouse_exited() -> void:
	if enabled:
		$Sprite2D.frame = 0

func _on_area_2d_input_event(_viewport: Node, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton && enabled:
		if event.pressed:
			$Sprite2D.frame = 2
			school_clicked.emit()
		else:
			$Sprite2D.frame = 1

func disable() -> void:
	enabled = false
	$Sprite2D.frame = 0
	
func enable() -> void:
	enabled = true
