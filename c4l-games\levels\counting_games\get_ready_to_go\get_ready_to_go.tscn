[gd_scene load_steps=14 format=3 uid="uid://bj64nkfn5qjxm"]

[ext_resource type="Texture2D" uid="uid://7v4t17i3m387" path="res://levels/counting_games/sprites/lot.png" id="1_nof0f"]
[ext_resource type="Script" uid="uid://jfhv186uajam" path="res://levels/counting_games/get_ready_to_go/get_ready_to_go.gd" id="1_vbw7l"]
[ext_resource type="Texture2D" uid="uid://exdbemiht5de" path="res://levels/counting_games/sprites/sky.png" id="2_c4hb8"]
[ext_resource type="PackedScene" uid="uid://bx5iw8ee0rmrm" path="res://levels/counting_games/general/scenes/count_cloud/count_cloud.tscn" id="3_c4hb8"]
[ext_resource type="PackedScene" uid="uid://o0xuwjbg5o4" path="res://levels/counting_games/general/scenes/bus/bus.tscn" id="4_gvhw8"]
[ext_resource type="PackedScene" uid="uid://cb7g6pn3dquh" path="res://levels/counting_games/general/scenes/students/standing_students/blue_standing_student.tscn" id="5_vbw7l"]
[ext_resource type="PackedScene" uid="uid://dbdry213f6qsr" path="res://levels/counting_games/general/scenes/students/standing_students/red_standing_student.tscn" id="7_nojst"]
[ext_resource type="PackedScene" uid="uid://ccym8qysxmhne" path="res://levels/counting_games/general/scenes/students/standing_students/purple_standing_student.tscn" id="8_xvbvl"]
[ext_resource type="PackedScene" uid="uid://wsqd16o7jado" path="res://levels/counting_games/general/scenes/students/standing_students/yellow_standing_student.tscn" id="9_apwwb"]
[ext_resource type="AudioStream" uid="uid://cd8wwxemhe2kw" path="res://levels/counting_games/general/audio/bus-fade.wav" id="10_cxjg1"]
[ext_resource type="PackedScene" uid="uid://bw1hwysqa14ib" path="res://generic/scenes/narrator/narrator.tscn" id="11_1f6if"]
[ext_resource type="PackedScene" uid="uid://cakvwk6r6comh" path="res://generic/scenes/buttons/help_button/help_button.tscn" id="12_5cieb"]
[ext_resource type="PackedScene" uid="uid://bh58tl2wuwhc4" path="res://generic/scenes/buttons/caption_button/caption_button.tscn" id="13_y76la"]

[node name="GetReadyToGo" type="Node2D"]
script = ExtResource("1_vbw7l")

[node name="Background" type="Node2D" parent="."]
position = Vector2(955, 540)
scale = Vector2(0.85, 0.76)

[node name="Sky" type="Sprite2D" parent="Background"]
texture = ExtResource("2_c4hb8")
offset = Vector2(0, -500)

[node name="ParkingLot" type="Sprite2D" parent="Background"]
texture = ExtResource("1_nof0f")

[node name="CountCloud" parent="." instance=ExtResource("3_c4hb8")]
position = Vector2(375, 155)
scale = Vector2(0.8, 0.8)

[node name="Bus" parent="." instance=ExtResource("4_gvhw8")]
position = Vector2(1358, 721)
scale = Vector2(0.75, 0.75)

[node name="Students" type="Node2D" parent="."]

[node name="StandingStudent1" parent="Students" instance=ExtResource("5_vbw7l")]
position = Vector2(1050, 800)

[node name="StandingStudent2" parent="Students" instance=ExtResource("7_nojst")]
position = Vector2(850, 800)

[node name="StandingStudent3" parent="Students" instance=ExtResource("8_xvbvl")]
position = Vector2(650, 800)

[node name="StandingStudent4" parent="Students" instance=ExtResource("9_apwwb")]
position = Vector2(450, 800)

[node name="StandingStudent5" parent="Students" instance=ExtResource("5_vbw7l")]
position = Vector2(250, 800)

[node name="StandingStudent6" parent="Students" instance=ExtResource("7_nojst")]
position = Vector2(1050, 600)

[node name="StandingStudent7" parent="Students" instance=ExtResource("8_xvbvl")]
position = Vector2(850, 600)

[node name="StandingStudent8" parent="Students" instance=ExtResource("9_apwwb")]
position = Vector2(650, 600)

[node name="StandingStudent9" parent="Students" instance=ExtResource("5_vbw7l")]
position = Vector2(450, 600)

[node name="StandingStudent10" parent="Students" instance=ExtResource("7_nojst")]
position = Vector2(250, 600)

[node name="Timer" type="Timer" parent="."]
wait_time = 5.0
one_shot = true

[node name="BusDriveAudio" type="AudioStreamPlayer" parent="."]
stream = ExtResource("10_cxjg1")

[node name="Narrator" parent="." instance=ExtResource("11_1f6if")]
position = Vector2(832, 115)

[node name="HelpButton" parent="." instance=ExtResource("12_5cieb")]
position = Vector2(50, 64)

[node name="CaptionButton" parent="." instance=ExtResource("13_y76la")]
position = Vector2(40, 178)

[connection signal="bus_returned" from="Bus" to="." method="_on_bus_bus_returned"]
[connection signal="student_clicked" from="Students/StandingStudent1" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent2" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent3" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent4" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent5" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent6" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent7" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent8" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent9" to="." method="_on_standing_student_student_clicked"]
[connection signal="student_clicked" from="Students/StandingStudent10" to="." method="_on_standing_student_student_clicked"]
[connection signal="timeout" from="Timer" to="." method="_on_timer_timeout"]
[connection signal="help_clicked" from="HelpButton" to="." method="_on_help_button_help_clicked"]
[connection signal="caption_clicked" from="CaptionButton" to="." method="_on_caption_button_caption_clicked"]
