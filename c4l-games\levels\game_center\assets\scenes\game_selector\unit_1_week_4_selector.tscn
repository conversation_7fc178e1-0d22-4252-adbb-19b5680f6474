[gd_scene load_steps=3 format=3 uid="uid://bcnxtrk04t7gv"]

[ext_resource type="PackedScene" uid="uid://3f4bp6mnacax" path="res://levels/game_center/assets/scenes/game_selector/game_selector.tscn" id="1_34qe1"]
[ext_resource type="Texture2D" uid="uid://ch03fviu5pbxq" path="res://levels/game_center/assets/images/C4L-Click-on-Objects-to-Count.png" id="2_fr31v"]

[node name="Unit1Week4Selector" instance=ExtResource("1_34qe1")]
offset_right = 497.0
game_scene = "res://levels/counting_games/get_ready_to_go/get_ready_to_go.tscn"
unit = 1
week = 4

[node name="Label" parent="PanelContainer/VBoxContainer/MarginContainer" index="0"]
text = "Unit 1, Week 4: Get Ready to Go"

[node name="TextureRect" parent="PanelContainer/VBoxContainer" index="1"]
texture = ExtResource("2_fr31v")
