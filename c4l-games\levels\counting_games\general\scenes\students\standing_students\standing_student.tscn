[gd_scene load_steps=3 format=3 uid="uid://8hvshnm2lvd"]

[ext_resource type="Script" uid="uid://bsyhx2246bty6" path="res://levels/counting_games/general/scenes/students/standing_students/standing_student.gd" id="1_g45k3"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_3vtg3"]
size = Vector2(98, 158)

[node name="StandingStudent" type="Node2D"]
script = ExtResource("1_g45k3")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
hframes = 2

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("RectangleShape2D_3vtg3")

[connection signal="input_event" from="Area2D" to="." method="_on_area_2d_input_event"]
