extends Node2D

var pre_load_slot: PackedScene = preload("res://levels/fruit_games/fruit_stands/scenes/crates/crate_slot/crate_slot.tscn")

var spawn_array: Array[Marker2D]
var slot_array: Array[Node2D]
var current_index: int

func _ready():
	spawn_array = [$Spawn1, $Spawn2, $Spawn3, $Spawn4, $Spawn5, $Spawn6, $Spawn7, $Spawn8, $Spawn9, $Spawn10]
	reset()

func reset() -> void:
	current_index = 0
	
	for slot in slot_array:
		slot.get_parent().remove_child(slot)
		slot.queue_free()
	slot_array.clear()
	
func set_crate_index_point() -> Marker2D:
	var currentMarger: Marker2D = spawn_array[current_index]
	current_index += 1
	return currentMarger
	
func get_index_marker(index: int) -> Marker2D:
	return spawn_array[index]
	
func remove_index() -> void:
	current_index -= 1

func spawn_slots(slot_count: int) -> void:
	for i in slot_count:
		var slot: Node2D = pre_load_slot.instantiate() as Node2D
		
		slot.position = spawn_array[i].position
		add_child(slot)
		slot.spawn_animation()
		slot_array.append(slot)
		await get_tree().create_timer(0.2).timeout
