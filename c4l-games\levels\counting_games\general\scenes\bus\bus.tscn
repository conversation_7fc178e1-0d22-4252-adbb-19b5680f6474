[gd_scene load_steps=4 format=3 uid="uid://o0xuwjbg5o4"]

[ext_resource type="Script" uid="uid://b5jcsdv8kqcbp" path="res://levels/counting_games/general/scenes/bus/bus.gd" id="1_i87mi"]
[ext_resource type="Texture2D" uid="uid://yifpdyctx117" path="res://levels/counting_games/sprites/bus.png" id="1_t24dk"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_t24dk"]
size = Vector2(317, 874)

[node name="Bus" type="CharacterBody2D"]
script = ExtResource("1_i87mi")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_t24dk")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(-0.5, -1)
shape = SubResource("RectangleShape2D_t24dk")

[node name="ChildSeat1" type="Marker2D" parent="."]
position = Vector2(-75, 145)

[node name="ChildSeat2" type="Marker2D" parent="."]
position = Vector2(-75, 15)

[node name="ChildSeat3" type="Marker2D" parent="."]
position = Vector2(-75, -110)

[node name="ChildSeat4" type="Marker2D" parent="."]
position = Vector2(-75, -239)

[node name="ChildSeat5" type="Marker2D" parent="."]
position = Vector2(-75, -365)

[node name="ChildSeat6" type="Marker2D" parent="."]
position = Vector2(75, 145)

[node name="ChildSeat7" type="Marker2D" parent="."]
position = Vector2(75, 15)

[node name="ChildSeat8" type="Marker2D" parent="."]
position = Vector2(75, -110)

[node name="ChildSeat9" type="Marker2D" parent="."]
position = Vector2(75, -239)

[node name="ChildSeat10" type="Marker2D" parent="."]
position = Vector2(75, -365)
