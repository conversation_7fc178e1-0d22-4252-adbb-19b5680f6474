[gd_scene load_steps=4 format=3 uid="uid://7df4pf3orom2"]

[ext_resource type="Texture2D" uid="uid://d06c54gl646f5" path="res://levels/fruit_games/sprites/C4L-ORC-Stand-Crate-01.png" id="1_f3upk"]
[ext_resource type="Script" uid="uid://bwyeqppbnvr7w" path="res://levels/fruit_games/fruit_stands/scenes/crates/large_crate/large_crate.gd" id="1_kvha0"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_f3upk"]
size = Vector2(566, 1094)

[node name="LargeCrate" type="Node2D"]
script = ExtResource("1_kvha0")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_f3upk")
hframes = 2

[node name="Spawn1" type="Marker2D" parent="."]
position = Vector2(-102, 360)

[node name="Spawn2" type="Marker2D" parent="."]
position = Vector2(-102, 180)

[node name="Spawn3" type="Marker2D" parent="."]
position = Vector2(-102, 0)

[node name="Spawn4" type="Marker2D" parent="."]
position = Vector2(-102, -182)

[node name="Spawn5" type="Marker2D" parent="."]
position = Vector2(-102, -363)

[node name="Spawn6" type="Marker2D" parent="."]
position = Vector2(104, 360)

[node name="Spawn7" type="Marker2D" parent="."]
position = Vector2(104, 180)

[node name="Spawn8" type="Marker2D" parent="."]
position = Vector2(104, 0)

[node name="Spawn9" type="Marker2D" parent="."]
position = Vector2(104, -182)

[node name="Spawn10" type="Marker2D" parent="."]
position = Vector2(104, -363)

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(1, -1)
shape = SubResource("RectangleShape2D_f3upk")
