extends Node2D

func _init():
	randomize()
	
func generate_simple_round_intro(fruit_single: String, fruit_plural: String, fruit_count: int) -> Array[String]:
	var round_intro: Array[String]
	
	var simple_stand_intro_num: int = randi_range(0, 1)
	if simple_stand_intro_num == 0:
		round_intro.append("action_drag_1.json")
		round_intro.append(fruit_plural)
		round_intro.append("target_crate_0.json")
	else:
		round_intro.append("action_add_0.json")
		round_intro.append(fruit_plural)
		round_intro.append("target_crate_1.json")
		round_intro.append("until.json")
		if fruit_count == 1:
			round_intro.append("statement_is.json")
		else:
			round_intro.append("statement_are.json")
			
		round_intro.append("number_" + str(fruit_count) + ".json")
		
		if fruit_count == 1:
			round_intro.append(fruit_single)
		else:
			round_intro.append(fruit_plural)
	
	return round_intro
	
func generate_simple_intro(fruit_single: String, fruit_plural: String, fruit_count: int) -> Array[String]:
	var created_intro: Array[String]
	var simple_stand_intro_num: int = randi_range(0, 1)
	created_intro.append("intro_stand_" + str(simple_stand_intro_num) + ".json")
	created_intro.append(fruit_plural)
	
	var round_intro: Array[String] = generate_simple_round_intro(fruit_single, fruit_plural, fruit_count)
	for json in round_intro:
		created_intro.append(json)
	return created_intro

func generate_simple_help(fruit_plural: String) -> Array[String]:
	var help_jsons: Array[String]
	
	help_jsons.append("action_add_1.json")
	help_jsons.append(fruit_plural)
	help_jsons.append("target_crate_1.json")
	help_jsons.append("action_click_2.json")
	
	return help_jsons

func generate_simple_round_success(fruit_single: String, fruit_plural: String, fruit_count: int) -> Array[String]:
	var json_array: Array[String]
	
	var round_start_num: int = randi_range(0, 4)
	if round_start_num < 4:
		json_array.append("congrats_" + str(round_start_num) + ".json")
	else:
		json_array.append("congrats_5.json")
	
	var round_variation_num: int = randi_range(0, 3)
	match round_variation_num:
		0:
			json_array.append("number_" + str(fruit_count) + ".json")
			if fruit_count == 1:
				json_array.append(fruit_single)
			else:
				json_array.append(fruit_plural)
				
			json_array.append("statement_sum_3.json")
		1:
			json_array.append("statement_produce_1.json")
			json_array.append(fruit_plural)
		2:
			if fruit_count == 1:
				json_array.append("statement_is.json")
			else:
				json_array.append("statement_are.json")
				
			json_array.append("number_" + str(fruit_count) + ".json")
			json_array.append("target_crate_5.json")
		3:
			json_array.append("statement_sum_0.json")
			json_array.append("number_" + str(fruit_count) + ".json")
			if fruit_count == 1:
				json_array.append(fruit_single)
			else:
				json_array.append(fruit_plural)

	return json_array

func generate_simple_outro(fruit_single: String) -> Array[String]:
	var outro_jsons: Array[String]
	
	outro_jsons.append("statement_count_3.json")
	outro_jsons.append(fruit_single)
	outro_jsons.append("orders.json")
	
	return outro_jsons
	
func generate_first_inccorect(fruit_plural: String)  -> Array[String]:
	var incorrect_jsons: Array[String]
	
	var variation: int = randi_range(0, 2)
	match variation:
		0:
			incorrect_jsons.append("action_place_0.json")
			incorrect_jsons.append(fruit_plural)
			incorrect_jsons.append("target_crate_6.json")
		1:
			incorrect_jsons.append("action_try_0.json")
		2:
			incorrect_jsons.append("action_add_1.json")
			incorrect_jsons.append(fruit_plural)
	
	return incorrect_jsons

func generate_second_inccorect(fruit_plural: String, fruit_count: int)  -> Array[String]:
	var incorrect_jsons: Array[String]
	
	incorrect_jsons.append("action_try_0.json")
	incorrect_jsons.append("action_produce_surely.json")
	incorrect_jsons.append(fruit_plural)
	incorrect_jsons.append("until.json")
	
	if fruit_count == 1:
		incorrect_jsons.append("statement_is.json")
	else:
		incorrect_jsons.append("statement_are.json")
	
	incorrect_jsons.append("number_" + str(fruit_count) + ".json")
	
	return incorrect_jsons

func generate_third_inccorect(fruit_count: int)  -> Array[String]:
	var incorrect_jsons: Array[String]
	
	incorrect_jsons.append("incorrect_0.json")
	incorrect_jsons.append("number_" + str(fruit_count) + ".json")
	incorrect_jsons.append("target_crate_5.json")
	
	return incorrect_jsons
