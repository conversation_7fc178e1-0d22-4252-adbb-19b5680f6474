[gd_scene load_steps=4 format=3 uid="uid://yh7kqwn42fdf"]

[ext_resource type="PackedScene" uid="uid://jlse36oa0tj1" path="res://levels/fruit_games/picnic/scenes/plates/plate.tscn" id="1_2tymq"]
[ext_resource type="Script" uid="uid://cb76c56pk1fvu" path="res://levels/fruit_games/picnic/scenes/plates/example_plate.gd" id="2_gkwgo"]

[sub_resource type="CircleShape2D" id="CircleShape2D_gkwgo"]
radius = 450.016

[node name="ExamplePlate" instance=ExtResource("1_2tymq")]
script = ExtResource("2_gkwgo")

[node name="SliceSprite1" type="Sprite2D" parent="." index="1"]
position = Vector2(128.75, -145)
hframes = 3

[node name="SliceSprite2" type="Sprite2D" parent="." index="2"]
position = Vector2(-123.75, 131.25)
hframes = 3

[node name="SliceSprite3" type="Sprite2D" parent="." index="3"]
position = Vector2(142.5, 113.75)
hframes = 3

[node name="SliceSprite4" type="Sprite2D" parent="." index="4"]
hframes = 3

[node name="SliceSprite5" type="Sprite2D" parent="." index="5"]
position = Vector2(-130, -113.75)
hframes = 3

[node name="Area2D" type="Area2D" parent="." index="6"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D" index="0"]
shape = SubResource("CircleShape2D_gkwgo")

[connection signal="input_event" from="Area2D" to="." method="_on_area_2d_input_event"]
