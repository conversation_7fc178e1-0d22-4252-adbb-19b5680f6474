[gd_scene load_steps=4 format=3 uid="uid://7du5s4nx71fm"]

[ext_resource type="Texture2D" uid="uid://s7d5recp71aa" path="res://assets/sprites/crossing-guard.png" id="1_8ykit"]
[ext_resource type="Script" uid="uid://djdae8rnytlkr" path="res://assets/scenes/crossing_guard/crossing_guard.gd" id="1_pugaj"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_pugaj"]
size = Vector2(199, 170)

[node name="CrossingGuard" type="Node2D"]
script = ExtResource("1_pugaj")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("1_8ykit")
hframes = 4

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(4.5, 0)
shape = SubResource("RectangleShape2D_pugaj")

[connection signal="input_event" from="Area2D" to="." method="_on_area_2d_input_event"]
[connection signal="mouse_entered" from="Area2D" to="." method="_on_area_2d_mouse_entered"]
[connection signal="mouse_exited" from="Area2D" to="." method="_on_area_2d_mouse_exited"]
